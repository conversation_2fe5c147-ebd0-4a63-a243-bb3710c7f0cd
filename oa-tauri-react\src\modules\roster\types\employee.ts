export type Employee = {
    index: number
    name: string
    jobNumber: string
    gender: string
    age: string
    status: string
    department: string
    position: string
    entryDate?: string
    contractDate?: string
    idCard: string
    phone: string
    address?: string
    education?: string
    major?: string
    bank?: string
    salary: string
    socialSecurity?: string
    accumulationFund?: string
    taxNumber?: string
    emergencyContact?: string
    remarks?: string
    departmentId: string
    birthDate?: string;
    lunarBirthday?: string;
    ethnicity?: string;
    nativePlace?: string;
    contactMethod?: string;
    email?: string;
    idNumber?: string;
    maritalStatus?: string;
    politicalStatus?: string;
    degree?: string;
    languageSkills?: string;
    currentAddress?: string;
    healthStatus?: string;
    idCardFront?: string;
    idCardBack?: string;
    hireDate?: string;
    workStatus?: string;
    contractType?: string;
    contractTerm?: string;
    relationship?: string;
    emergencyContactNumber?: string;
    familyMembers?: any[];
    workExperiences?: any[];
    educationExperiences?: any[];
    trainingExperiences?: any[];
    awards?: any[];
    punishments?: any[];
    performanceReviews?: any[];
    salaryChanges?: any[];
    housingFund?: any;
    commercialInsurance?: any;
    title?: string;
    leaveDate?: string;
    leaveReason?: string;
    rehireDate?: string;
    rehireReason?: string;
    otherNotes?: string;
    lastUpdated?: string;
    height?: string;
    weight?: string;
    backgroundCheck?: string;
    academicDegree?: string;
    emergencyRelation?: string;
    notes?: string;
} 