import { useCalendar } from './hooks/useCalendar'
import { useTopModules } from './hooks/useTopModules'
import { TopModules } from './components/TopModules'
import { CalendarHeader } from './components/CalendarHeader'
import { CalendarGrid } from './components/CalendarGrid'

export default function CalendarHomePage() {
    const {
        calendarData,
        goToPreviousMonth,
        goToNextMonth,
        goToToday
    } = useCalendar()

    const { modules, handleModuleClick } = useTopModules()

    const handleDayClick = (day: any) => {
        console.log('Day clicked:', day)
        // 可以在这里添加日期点击的逻辑
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
            <div className="p-6 space-y-6">
                {/* 顶部功能模块 */}
                <TopModules
                    modules={modules}
                    onModuleClick={handleModuleClick}
                />

                {/* 日历头部 */}
                <CalendarHeader
                    year={calendarData.year}
                    monthName={calendarData.monthName}
                    onPreviousMonth={goToPreviousMonth}
                    onNextMonth={goToNextMonth}
                    onToday={goToToday}
                />

                {/* 日历网格 */}
                <CalendarGrid
                    weekDays={calendarData.weekDays}
                    days={calendarData.days}
                    onDayClick={handleDayClick}
                />
            </div>
        </div>
    )
}

export const Component = CalendarHomePage
