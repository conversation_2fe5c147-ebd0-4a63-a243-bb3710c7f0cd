import { useState } from 'react'
import { useNavigate } from 'react-router'

// 顶部功能模块配置
const topModules = [
    { icon: "👤", label: "私人信息", path: "/digital-human/info" },
    { icon: "💼", label: "工作台", path: "/workspace" },
    { icon: "🏢", label: "会议室", path: "/meeting-room" },
    { icon: "💾", label: "网络硬盘", path: "/network-disk" },
    { icon: "🔧", label: "国产源码", path: "/source-code" },
    { icon: "🤖", label: "数字人", path: "/digital-human/info" },
    { icon: "🏥", label: "健康信息", path: "/health-info" },
    { icon: "📅", label: "员工日历", path: "/employee-calendar" },
    { icon: "👥", label: "成员信息", path: "/member-info" },
]

// 生成日历数据
const generateCalendarData = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = today.getMonth()
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    
    // 获取当月第一天是星期几（0=周日，1=周一...）
    const firstDayOfWeek = firstDay.getDay()
    
    // 生成日历数组
    const calendar = []
    
    // 添加上个月的日期（填充）
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        const date = new Date(year, month, -i)
        calendar.push({
            date: date.getDate(),
            isCurrentMonth: false,
            isToday: false,
            fullDate: date
        })
    }
    
    // 添加当月的日期
    for (let date = 1; date <= lastDay.getDate(); date++) {
        const currentDate = new Date(year, month, date)
        calendar.push({
            date,
            isCurrentMonth: true,
            isToday: date === today.getDate() && month === today.getMonth() && year === today.getFullYear(),
            fullDate: currentDate
        })
    }
    
    // 添加下个月的日期（填充到42个格子）
    const remainingCells = 42 - calendar.length
    for (let date = 1; date <= remainingCells; date++) {
        const nextMonthDate = new Date(year, month + 1, date)
        calendar.push({
            date,
            isCurrentMonth: false,
            isToday: false,
            fullDate: nextMonthDate
        })
    }
    
    return calendar
}

// 中文数字转换
const getChineseNumber = (num: number) => {
    const chinese = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十', '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十', '三十一']
    return chinese[num] || num.toString()
}

export default function CalendarHomePage() {
    const navigate = useNavigate()
    const [currentDate] = useState(new Date())
    const calendarData = generateCalendarData()
    
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    
    const handleModuleClick = (module: typeof topModules[0]) => {
        if (module.path) {
            navigate(module.path)
        }
    }
    
    return (
        <div className="min-h-screen bg-gray-50 p-6">
            {/* 顶部功能模块 */}
            <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-9 gap-4 mb-8">
                {topModules.map((module, index) => (
                    <div
                        key={index}
                        className="bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-shadow flex flex-col items-center gap-2"
                        onClick={() => handleModuleClick(module)}
                    >
                        <div className="text-2xl">{module.icon}</div>
                        <span className="text-sm text-gray-700 text-center">{module.label}</span>
                    </div>
                ))}
            </div>
            
            {/* 日历标题 */}
            <div className="bg-white rounded-lg shadow-sm mb-4">
                <div className="flex items-center justify-between p-4 border-b">
                    <div className="flex items-center gap-4">
                        <h2 className="text-xl font-bold">{year}年</h2>
                        <div className="flex items-center gap-2">
                            <button className="px-3 py-1 text-sm bg-gray-100 rounded hover:bg-gray-200">
                                &lt;
                            </button>
                            <span className="text-lg font-medium">{monthNames[month]}</span>
                            <button className="px-3 py-1 text-sm bg-gray-100 rounded hover:bg-gray-200">
                                &gt;
                            </button>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <button className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">今天</button>
                        <button className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">周</button>
                        <button className="px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600">月</button>
                        <button className="px-3 py-1 text-sm bg-orange-500 text-white rounded hover:bg-orange-600">年</button>
                    </div>
                </div>
                
                {/* 星期标题 */}
                <div className="grid grid-cols-7 border-b">
                    {weekDays.map((day, index) => (
                        <div key={index} className="p-3 text-center text-sm font-medium text-gray-600 border-r last:border-r-0">
                            {day}
                        </div>
                    ))}
                </div>
                
                {/* 日历网格 */}
                <div className="grid grid-cols-7">
                    {calendarData.map((day, index) => (
                        <div
                            key={index}
                            className={`
                                min-h-[120px] p-2 border-r border-b last:border-r-0 
                                ${day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                                ${day.isToday ? 'bg-blue-50' : ''}
                                hover:bg-gray-50 cursor-pointer
                            `}
                        >
                            <div className="flex flex-col h-full">
                                <div className={`
                                    text-sm mb-1 flex items-center justify-between
                                    ${day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                                    ${day.isToday ? 'font-bold' : ''}
                                `}>
                                    <span>{day.date}</span>
                                    <span className="text-xs">{getChineseNumber(day.date)}</span>
                                </div>
                                {day.isToday && (
                                    <div className="w-6 h-6 bg-blue-500 rounded text-white text-xs flex items-center justify-center mb-1">
                                        {day.date}
                                    </div>
                                )}
                                <div className="flex-1 text-xs text-gray-500">
                                    {/* 这里可以添加日程事件 */}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}

export const Component = CalendarHomePage
