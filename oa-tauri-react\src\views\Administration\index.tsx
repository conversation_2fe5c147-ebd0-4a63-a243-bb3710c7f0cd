import { useState } from "react"
import { AdminSidebar } from "./components/admin-sidebar"
import Toolbar from "../Roster/component/toolbar"
import { TemplateDetailPanel } from '@/modules/template/components/TemplateDetailPanel'

// // 管理模块类型
// type AdminModule = 'attendance' | 'salary' | 'leave' | 'birthday'

// // 模拟员工生日数据
// const EMPLOYEE_BIRTHDAY_DATA = [
//   {
//     id: 1,
//     sequence: 1,
//     name: "张三",
//     birthDate: "1995-05-05",
//     lunarBirthday: "四月初六",
//     gender: "男",
//     healthStatus: "良好",
//     department: "内打云",
//     jobNumber: "8169",
//     hireDate: "2025-05-19"
//   },
//   {
//     id: 2,
//     sequence: 2,
//     name: "李四",
//     birthDate: "1998-09-12",
//     lunarBirthday: "八月十五",
//     gender: "女",
//     healthStatus: "良好",
//     department: "内打云",
//     jobNumber: "8168",
//     hireDate: "2024-10-11"
//   }
// ]

export function AdministrationPage() {
  const [selectedId, setSelectedId] = useState<number | undefined>(undefined)

  return (
    <div className="h-screen bg-gray-50">
      <Toolbar isShowLeftArrow={true} />
      <div className="flex h-[calc(100vh-2.5rem)] bg-gray-50">
        <AdminSidebar selectedId={selectedId} onSelect={setSelectedId} />
        <div className="flex-1 overflow-hidden border">
          <TemplateDetailPanel templateId={selectedId} />
        </div>
      </div>
    </div>
  )
}

// Necessary for react router to lazy load.
export const Component = AdministrationPage 