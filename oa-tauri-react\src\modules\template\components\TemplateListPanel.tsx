import React from 'react';
import { useTemplateList, TemplateItem } from '../hooks/useTemplateList';

interface TemplateListPanelProps {
  selectedId?: number;
  onSelect: (id: number) => void;
}

export const TemplateListPanel: React.FC<TemplateListPanelProps> = ({ selectedId, onSelect }) => {
  const { templates, loading, error } = useTemplateList();

  return (
    <div className="h-64 overflow-y-auto border-b bg-white">
      <div className="p-4 font-bold text-lg border-b">模板列表</div>
      {loading ? (
        <div className="p-4 text-gray-400">加载模板列表中...</div>
      ) : error ? (
        <div className="p-4 text-red-500">{error}</div>
      ) : (
        <ul>
          {templates.map((tpl: TemplateItem) => (
            <li
              key={tpl.id}
              className={`cursor-pointer px-4 py-3 border-b hover:bg-blue-50 transition-colors ${selectedId === tpl.id ? 'bg-blue-100 text-blue-700 font-semibold' : ''}`}
              onClick={() => onSelect(tpl.id)}
            >
              <div className="flex justify-between items-center">
                <span className="text-base">{tpl.template_name}</span>
                <span className="text-xs text-gray-400 ml-2">ID: {tpl.id}</span>
              </div>
              <div className="text-xs text-gray-500 mt-1">{tpl.template_description}</div>
              <div className="text-xs text-gray-400 mt-1">统计范围: {tpl.team_ids?.join(', ') || '-'}</div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}; 