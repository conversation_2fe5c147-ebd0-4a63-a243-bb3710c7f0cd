import React, { useEffect, useState, useRef } from 'react';
import { RosterProvider, useRosterContext } from '@/modules/roster/context/RosterContext';
import { RosterToolbar } from '@/modules/roster/components/RosterToolbar';
import { EmployeeDetailPanel } from '@/modules/roster/components/EmployeeDetailPanel';
import { PersonalOrgSidebar } from '@/modules/roster/components/RosterSidebar';
import { useRosterTabs } from '@/modules/roster/hooks/useRosterTabs';
import { usePersonalOrgSidebar } from '@/modules/roster/hooks/usePersonalOrgSidebar';
import { useClassifyTabs } from '@/modules/roster/hooks/useClassifyTabs';
import { ClassifyTabs } from '@/modules/roster/components/ClassifyTabs';
import type { EmployeeDetailPanelRef } from '@/modules/roster/components/EmployeeDetailPanel';

const RosterPageInner: React.FC = () => {
  const { teamList, classifyList, loading } = useRosterContext();
  // 顶部tab
  const { activeTab, onTabChange } = useRosterTabs('');
  // 组织架构成员
  const { selectedUserId, setSelectedUserId } = usePersonalOrgSidebar(teamList);
  // 当前分类项
  const currentClassify = classifyList.find(c => c.classify === activeTab);
  // 二级tab
  const { selectedCategory, setSelectedCategory } = useClassifyTabs(currentClassify);
  // 当前副标题项
  const currentSubItem = currentClassify?.sub_items?.find((sub: any) => sub.category === selectedCategory);
  // 是否显示ClassifyTabs保存按钮
  const showSaveButton = currentSubItem && !currentSubItem.has_many_records;
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const detailPanelRef = useRef<EmployeeDetailPanelRef>(null);
  // 是否为表格模式
  const isTableMode = !!currentSubItem?.has_many_records;
  // 是否隐藏ClassifyTabs按钮（表格模式且编辑时）
  const hideWhenEditing = isTableMode && isEditing;

  // 首次加载后自动选中第一个tab
  useEffect(() => {
    if (classifyList && classifyList.length > 0 && !activeTab) {
      onTabChange(classifyList[0].classify);
    }
  }, [classifyList, activeTab, onTabChange]);

  // 首次加载后自动选中第一个成员
  useEffect(() => {
    if (Array.isArray(teamList) && teamList.length > 0 && !selectedUserId) {
      outer: for (const company of teamList) {
        if (company.children && company.children.length > 0) {
          for (const group of company.children) {
            if (group.children && group.children.length > 0) {
              setSelectedUserId(group.children[0].id);
              break outer;
            }
          }
        }
      }
    }
  }, [teamList, selectedUserId, setSelectedUserId]);

  // 切换副标题时自动退出编辑状态
  useEffect(() => {
    setIsEditing(false);
  }, [selectedCategory]);

  // 组织架构成员点击
  const handleSelectUser = (userId: number) => {
    setSelectedUserId(userId);
    // 切换成员时，重置二级tab为第一个
    if (currentClassify && currentClassify.sub_items.length > 0) {
      setSelectedCategory(currentClassify.sub_items[0].category);
    }
  };
  // 二级tab切换
  const handleSelectCategoryTab = (category: string) => {
    setSelectedCategory(category);
  };

  const handleEdit = () => setIsEditing(prev => !prev);
  const handleSave = async () => {
    setSaving(true);
    try {
      if (detailPanelRef.current && typeof detailPanelRef.current.save === 'function') {
        await detailPanelRef.current.save();
      }
      setIsEditing(false);
    } catch (e: any) {
      // window?.alert(e?.message || '保存失败');
      console.log(e);
    } finally {
      setSaving(false);
    }
  };

  // loading判断优化：只要classifyList和teamList都已获取（即使为空数组）即可渲染
  if (loading || !Array.isArray(classifyList) || !teamList || typeof teamList !== 'object') {
    return <div className="flex items-center justify-center h-screen text-gray-400">正在加载...</div>;
  }

  return (
    <div className="h-screen bg-gray-50">
      <RosterToolbar activeTab={activeTab} onTabChange={onTabChange} />
      <div className="flex h-[calc(100vh-3.5rem)] bg-gray-50">
        <PersonalOrgSidebar
          selectedUserId={selectedUserId}
          onSelectUser={handleSelectUser}
        />
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-auto p-4 border">
            {selectedUserId && selectedCategory && currentClassify ? (
                <>
                  <ClassifyTabs
                    classifyItem={currentClassify}
                    activeTab={selectedCategory}
                    onSelectCategoryTab={handleSelectCategoryTab}
                    isEditing={isEditing}
                    saving={saving}
                    onEdit={handleEdit}
                    onSave={handleSave}
                    showSaveButton={showSaveButton}
                    hideWhenEditing={hideWhenEditing}
                  />
                  <EmployeeDetailPanel
                    ref={detailPanelRef}
                    userId={selectedUserId}
                    category={selectedCategory}
                    classifyItem={currentClassify}
                    isEditing={isEditing}
                    onSave={handleSave}
                  />
                </>
              ) : (
                <div className="text-gray-400 flex items-center justify-center h-full">暂无数据</div>
              )
            }
          </div>
        </div>
      </div>
    </div>
  );
};

export const Component = () => (
  <RosterProvider>
    <RosterPageInner />
  </RosterProvider>
);
