# 数字人模块

这个模块包含了数字人相关的页面和组件，采用了模块化的架构设计。

## 文件结构

```
DigitalHuman/
├── CalendarHomePage.tsx          # 日历首页主组件
├── index.tsx                     # 数字人信息页面
├── components/                   # 可复用组件
│   ├── TopModules.tsx           # 顶部功能模块组件
│   ├── CalendarHeader.tsx       # 日历头部组件
│   └── CalendarGrid.tsx         # 日历网格组件
├── hooks/                       # 自定义 Hooks
│   ├── useCalendar.ts          # 日历逻辑 Hook
│   └── useTopModules.ts        # 顶部模块逻辑 Hook
├── constants/                   # 常量配置
│   └── styles.ts               # 样式常量
└── README.md                   # 说明文档
```

## 组件说明

### CalendarHomePage
主要的日历首页组件，整合了所有子组件和 hooks。

**特性：**
- 响应式设计
- 美观的渐变背景
- 模块化架构

### TopModules
顶部功能模块组件，展示各种功能入口。

**特性：**
- 悬浮动画效果
- 响应式网格布局
- 可配置的模块数据

### CalendarHeader
日历头部组件，包含年月导航和视图切换。

**特性：**
- 月份导航
- 今天按钮
- 视图切换按钮（周/月/年）

### CalendarGrid
日历网格组件，显示日期和事件。

**特性：**
- 今日高亮
- 事件显示
- 日期点击交互

## Hooks 说明

### useCalendar
管理日历状态和逻辑的 Hook。

**返回值：**
- `calendarData`: 日历数据
- `goToPreviousMonth`: 上一月
- `goToNextMonth`: 下一月
- `goToToday`: 回到今天

### useTopModules
管理顶部功能模块的 Hook。

**返回值：**
- `modules`: 模块列表
- `handleModuleClick`: 模块点击处理

## 样式系统

使用 Tailwind CSS 和自定义样式常量：

- **颜色系统**: 统一的颜色配置
- **阴影效果**: 一致的阴影样式
- **过渡动画**: 流畅的交互动画
- **响应式设计**: 适配不同屏幕尺寸

## 路由配置

- `/digital-human` - 日历首页
- `/digital-human/info` - 数字人信息页面

## 使用方式

```tsx
import CalendarHomePage from './CalendarHomePage'

// 在路由中使用
<Route path="/digital-human" component={CalendarHomePage} />
```

## 扩展指南

### 添加新的功能模块

1. 在 `useTopModules.ts` 中添加新模块配置
2. 创建对应的路由和页面组件
3. 更新模块的图标和描述

### 自定义日历事件

1. 扩展 `CalendarDay` 接口添加事件字段
2. 在 `CalendarGrid` 组件中渲染事件
3. 添加事件管理的 Hook

### 样式定制

1. 修改 `constants/styles.ts` 中的样式常量
2. 使用 Tailwind CSS 的配置文件进行全局定制
3. 添加自定义 CSS 类

## 性能优化

- 使用 `useMemo` 缓存日历计算
- 组件懒加载
- 图片和资源优化
- 合理的重新渲染控制
