import React, { useState } from 'react';

import { FieldRenderer } from './FieldRenderer';

export interface Field {
  id: string;
  field_name: string;
  field_type: string;
  data_type?: string;
  is_required?: boolean;
  is_visible?: boolean;
  allow_multiple?: boolean;
  options?: Array<string>;
  length?: number;
  category?: string;
  remark?: string;
  description?: string;
}

export type FormValues = Record<string, unknown>;

/**
 * 动态表单组件
 * @param fields 字段配置数组
 * @param values 当前表单值
 * @param isEditing 是否为编辑模式
 * @param onChange 表单值变更回调
 */
export interface DynamicFormProps {
  fields: Field[];
  values: FormValues;
  isEditing: boolean;
  onChange: (values: FormValues) => void;
}

// const FIELD_TYPE_MAP: Record<string, string> = {
//   MultilineText: '多行文本',
//   SingleLineText: '单行文本',
//   Date: '日期',
//   DateTime: '日期时间',
//   CustomSelector: '自定义选择器',
//   Number: '数字',
//   Team: '团队',
//   User: '用户',
//   Image: '图片',
//   Sex: '性别',
// };


export const DynamicForm: React.FC<DynamicFormProps> = ({ fields, values, isEditing, onChange }) => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 组织/用户弹窗状态提升到父组件（此处暂用本地状态，后续可抽离）
  const [teamSelector, setTeamSelector] = useState<{visible: boolean, fieldId: string | null}>({visible: false, fieldId: null});
  const [userSelector, setUserSelector] = useState<{visible: boolean, fieldId: string | null}>({visible: false, fieldId: null});

  const handleFieldChange = (id: string, value: any) => {
    onChange({ ...values, [id]: value });
    if (errors[id]) {
      setErrors(prev => ({ ...prev, [id]: '' }));
    }
  };

  // 校验所有必填项
  const validateAll = () => {
    const newErrors: Record<string, string> = {};
    fields.forEach(field => {
      if (field.is_required && !values[field.id]) {
        newErrors[field.id] = `${field.field_name}为必填项`;
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 外部可调用 validateAll
  React.useImperativeHandle((onChange as { ref?: React.RefObject<{ validateAll: () => boolean }> }).ref, () => ({ validateAll }), [fields, values]);

  return (
    <div className="bg-white rounded-xl shadow-md p-4  mx-auto">
      <form className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
        {fields.filter(f => f.is_visible !== false).map((field) => {
          const value = values[field.id] ?? (field.allow_multiple ? [] : '');
          const label = field.field_name;
          const required = field.is_required;
          const remark = field.remark || field.description;
          return (
            <div
              key={field.id}
              className="flex w-full items-start"
            >
              <label className="font-medium text-gray-700 w-full md:w-32 flex-shrink-0 flex items-center mb-0 md:mb-0">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
              </label>
              <div className="flex-1">
                {remark && <div className="text-xs text-gray-400 mb-1">{remark}</div>}
                <FieldRenderer
                  field={field}
                  value={value}
                  isEditing={isEditing}
                  error={errors[field.id]}
                  onChange={val => handleFieldChange(field.id, val)}
                  teamSelector={{
                    visible: teamSelector.visible,
                    fieldId: teamSelector.fieldId,
                    setVisible: (visible, fieldId) => setTeamSelector({ visible, fieldId }),
                  }}
                  userSelector={{
                    visible: userSelector.visible,
                    fieldId: userSelector.fieldId,
                    setVisible: (visible, fieldId) => setUserSelector({ visible, fieldId }),
                  }}
                />
                {errors[field.id] && <div className="text-xs text-red-500 mt-1">{errors[field.id]}</div>}
              </div>
            </div>
          );
        })}
      </form>
    </div>
  );
}; 