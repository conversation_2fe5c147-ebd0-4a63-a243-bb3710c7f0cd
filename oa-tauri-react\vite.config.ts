import path from 'path'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react(), tailwindcss()],

    // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
    build: {
        rollupOptions: {
            input: {
                main: path.resolve(__dirname, 'index.html'),
                // database: path.resolve(__dirname, 'src/windows/database/index.html'),
                // detail: path.resolve(__dirname, 'src/windows/detail/index.html'),
                // log: path.resolve(__dirname, 'src/windows/log/index.html'),
                // migrate: path.resolve(__dirname, 'src/windows/migrate/index.html'),
                // session: path.resolve(__dirname, 'src/windows/session/index.html')
            }
        }
    },
    // 1. prevent vite from obscuring rust errors
    clearScreen: false,
    // 2. tauri expects a fixed port, fail if that port is not available
    server: {
        host: '0.0.0.0',
        port: 1422,
        watch: {
            // 3. tell vite to ignore watching `src-tauri`
            ignored: ['**/src-tauri/**']
        },
        proxy: {
            '/api': {
                target: 'http://*************:18080/',
                changeOrigin: true,
            },
        },
    },

    // Shadcn UI
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src')
        }
    }
})
