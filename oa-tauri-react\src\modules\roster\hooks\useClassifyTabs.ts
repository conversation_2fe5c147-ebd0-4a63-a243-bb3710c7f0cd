import { useState, useEffect } from 'react';
import { ClassifyItem } from '../types/classify';

export function useClassifyTabs(classifyItem: ClassifyItem | undefined) {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  useEffect(() => {
    if (classifyItem && classifyItem.sub_items.length > 0) {
      setSelectedCategory(classifyItem.sub_items[0].category);
    }
  }, [classifyItem]);
  return { selectedCategory, setSelectedCategory };
} 