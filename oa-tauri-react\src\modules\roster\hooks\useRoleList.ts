import { useEffect, useState } from 'react';
import { getListUserAll } from '@/api/roster';
import type { Role } from '../types/role';

// 全局缓存
let rolesCache: Role[] | null = null;
let loadingCache = false;
let errorCache: Error | null = null;
let promiseCache: Promise<Role[]> | null = null;

export function useRoleList() {
  const [roles, setRoles] = useState<Role[]>(rolesCache || []);
  const [loading, setLoading] = useState(loadingCache);
  const [error, setError] = useState<Error | null>(errorCache);

  useEffect(() => {
    if (rolesCache) {
      setRoles(rolesCache);
      setLoading(false);
      setError(null);
      return;
    }
    if (loadingCache && promiseCache) {
      setLoading(true);
      promiseCache.then((data) => {
        setRoles(data);
        setLoading(false);
        setError(null);
      }).catch((err) => {
        setError(err);
        setLoading(false);
      });
      return;
    }
    // 首次加载
    loadingCache = true;
    setLoading(true);
    const p = getListUserAll({})
      .then((res: any) => {
        const list = res?.data?.records || [];
        rolesCache = list;
        loadingCache = false;
        errorCache = null;
        setRoles(list);
        setLoading(false);
        setError(null);
        return list;
      })
      .catch((err: any) => {
        errorCache = err;
        loadingCache = false;
        setError(err);
        setLoading(false);
        throw err;
      });
    promiseCache = p;
  }, []);

  return { roles, loading, error };
} 