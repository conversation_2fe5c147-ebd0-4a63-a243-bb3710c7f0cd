import { FunctionComponent } from "react";
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
    navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Link } from "@radix-ui/react-navigation-menu";
import { ChevronLeft } from "lucide-react";
import { useClassifyList } from '@/hooks/useClassifyList'

interface ToolbarProps {
    isShowLeftArrow?: boolean
}

const Toolbar: FunctionComponent<ToolbarProps> = (props: ToolbarProps) => {
    const { data: classifyList } = useClassifyList();
    if (props.isShowLeftArrow) {
        return (
            <NavigationMenu>
                <NavigationMenuList className="p-2">
                    <NavigationMenuItem className="ml-2" >
                        <Link href="/" >
                            <ChevronLeft />
                        </Link>
                    </NavigationMenuItem>
                </NavigationMenuList>
            </NavigationMenu>
        );
    } else {
        // hide left arrow
        return (
            <NavigationMenu>
                <NavigationMenuList className="p-2">
                    <NavigationMenuItem className="ml-2" >
                        <Link href="/" >
                            <ChevronLeft />
                        </Link>
                    </NavigationMenuItem>
                    {/* 动态分类菜单 */}
                    {classifyList && classifyList.map(classify => (
                        <NavigationMenuItem className="ml-2" key={classify.classify}>
                            <NavigationMenuTrigger>{classify.description}</NavigationMenuTrigger>
                            <NavigationMenuContent>
                                <div className="grid gap-1 p-2 w-48">
                                    {classify.sub_items.map(sub => (
                                        <NavigationMenuLink className="p-2 hover:bg-slate-100 rounded-md" key={sub.category}>
                                            {sub.description}
                                        </NavigationMenuLink>
                                    ))}
                                </div>
                            </NavigationMenuContent>
                        </NavigationMenuItem>
                    ))}
                    {/* 其它静态菜单项可保留或移除 */}
                    <NavigationMenuItem>
                        <NavigationMenuLink className={navigationMenuTriggerStyle()} href="/roster">
                            其它
                        </NavigationMenuLink>
                    </NavigationMenuItem>
                </NavigationMenuList>
            </NavigationMenu>
        );
    }
}

export default Toolbar;
