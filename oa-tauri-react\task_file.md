# Context
Filename: task_file.md
Created On: 2024-07-30
Created By: AI
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
用户希望修复 `npm run tauri dev` 命令的传参报错，并确保正式版应用程序 `ManagerApp.exe` 能够正确解析命令行参数 `-p / -t 2ivgKZGcnK5ISpHbCK7sLAMA7Ar0nm7o -u 127.0.0.1`。

# Project Overview
Tauri React 应用，需要处理 URL 查询参数 (开发模式) 和命令行参数 (正式版)。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)
从 `src/main.tsx` 中确定了 `token` 变量的声明位置，并确定了通过 `window.location.search` 获取 URL 查询参数，然后使用 `URLSearchParams` 解析 `token` 是最合适的方法（针对开发模式）。

用户最新的错误信息 `error: Found argument '/' which wasn't expected, or isn't valid in this context` 以及 `USAGE: create-tauri-core.exe --page <page> --token <token> --uri <uri>` 明确指出应用程序期望的是命名命令行参数（`--page`, `--token`, `--uri`）而非位置参数。

这意味着需要：
1.  配置 `src-tauri/tauri.conf.json` 来定义这些命名命令行参数。
2.  在 `src/main.tsx` 中使用 `@tauri-apps/api/cli` 来读取和解析这些命名参数。

# Proposed Solution (Populated by INNOVATE mode)
提议的解决方案是：
1.  **对于开发模式:** 沿用之前的方法，即通过 `npm run tauri dev` 启动，并通过 URL 查询参数传递 `token`。如果需要在开发模式下测试命令行参数，则使用 `npm run tauri dev -- --page / --token <value> --uri <value>` 这种双重 `--` 的格式。
2.  **对于正式版应用:**
    a.  安装 `@tauri-apps/plugin-cli` 插件。
    b.  在 `src-tauri/tauri.conf.json` 中配置 `cli`，定义 `page` (短名 `p`), `token` (短名 `t`), `uri` (短名 `u`) 为命名参数，并指定它们 `takesValue`。
    c.  修改 `src/main.tsx`：导入 `getMatches`，在应用程序启动时异步获取命令行参数，并根据当前运行环境（通过 `__TAURI_DEBUG__` 判断）来决定从 URL 查询参数或命令行参数中获取所需的值。移除所有临时调试代码。
3.  **解决端口占用问题:** 如果仍然存在，检查并解决：终止占用端口的进程，或者修改 `vite.config.ts` 和 `src-tauri/tauri.conf.json` 中的端口号。

# Implementation Plan (Generated by PLAN mode)

```
Implementation Checklist:
1. 安装 `@tauri-apps/plugin-cli` 依赖。
2. 在 `src-tauri/tauri.conf.json` 中配置 `cli`，定义 `page` (短名 `p`), `token` (短名 `t`), `uri` (短名 `u`) 命令行参数，并确保它们 `takesValue`。
3. 修改 `src/main.tsx` 文件：
    a. 导入 `getMatches` 函数。
    b. 在应用程序启动时，异步获取命令行参数。
    c. 根据应用程序运行环境（`__TAURI_DEBUG__` 变量判断），优先从 URL 查询参数获取 `token` (开发模式)，否则从命令行参数获取 `token` (`matches.args.token.value`)。
    d. 移除 `src/main.tsx` 中临时添加的 `console.log` 和 `alert` 调试代码（如果之前未完全移除）。
    e. 将获取到的命令行参数值（例如 `page`, `uri`）存储到应用程序中可访问的位置。
4. 提醒用户开发模式下使用 `npm run tauri dev` 启动，并在需要传递参数时使用 `npm run tauri dev -- --page / --token <value> --uri <value>` 的格式。生产模式下使用 `ManagerApp.exe --page / --token <value> --uri <value>` 格式启动。
5. 确认端口占用问题已解决或配置已更新（如果之前未解决）。
```

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: ""

# Task Progress (Appended by EXECUTE mode after each step completion) 