{"name": "管理系统", "version": "1.0.0", "description": "A project template for creating a Tauri app with Vite, React, and Tailwind CSS.", "keywords": ["shadcn", "tauri", "vite", "react", "tailwindcss", "ui", "desktop"], "homepage": "https://github.com/MrLightful/create-tauri-core", "author": {"name": "MrLightful", "url": "https://mrlightful.com"}, "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "prepare": "husky", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ."}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.4", "@stagewise-plugins/react": "^0.4.7", "@stagewise/toolbar-react": "^0.4.7", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-table": "^8.21.3", "@types/axios": "^0.14.4", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.506.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-router": "^7.5.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.25.1", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "3.5.3", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.8", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1", "vite": "^6.3.4"}}