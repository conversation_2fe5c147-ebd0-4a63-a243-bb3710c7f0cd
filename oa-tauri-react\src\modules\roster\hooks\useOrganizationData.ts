import { useEffect, useState, useCallback } from 'react';
import { getTeamListApi } from '@/api/roster';
import { teamListToTree, getAllUsersFromTree } from '@/utils/tree';
import { UserItem, OrgTreeItem, TeamListItem } from '../types/team';

interface OrganizationData {
  teamTreeData: TeamListItem[];
  userList: UserItem[];
  loading: boolean;
  error: any;
  refresh: () => void;
}

let organizationDataCache: {
  teamTree: TeamListItem[];
  users: UserItem[];
} | null = null;
let organizationPromiseCache: Promise<{
  teamTree: TeamListItem[];
  users: UserItem[];
}> | null = null;

export function useOrganizationData(): OrganizationData {
  const [teamTreeData, setTeamTreeData] = useState<TeamListItem[]>(
    organizationDataCache?.teamTree || []
  );
  const [userList, setUserList] = useState<UserItem[]>(
    organizationDataCache?.users || []
  );
  const [loading, setLoading] = useState<boolean>(!organizationDataCache);
  const [error, setError] = useState<any>(null);

  const fetchData = useCallback(() => {
    if (organizationDataCache) {
      setTeamTreeData(organizationDataCache.teamTree);
      setUserList(organizationDataCache.users);
      setLoading(false);
      setError(null);
      return;
    }

    if (organizationPromiseCache) {
      organizationPromiseCache
        .then(data => {
          setTeamTreeData(data.teamTree);
          setUserList(data.users);
          setLoading(false);
          setError(null);
        })
        .catch(err => {
          setError(err);
          setLoading(false);
        });
      return;
    }

    setLoading(true);
    setError(null);

    const p = getTeamListApi()
      .then(res => {
        const rawList: OrgTreeItem[] = res?.data || res?.d || [];
        const tree = teamListToTree(rawList);
        const users = getAllUsersFromTree(tree);

        organizationDataCache = { teamTree: tree, users };
        organizationPromiseCache = null;

        setTeamTreeData(tree);
        setUserList(users);
        setLoading(false);
        return { teamTree: tree, users };
      })
      .catch(err => {
        setError(err);
        setLoading(false);
        organizationPromiseCache = null;
        throw err;
      });

    organizationPromiseCache = p;
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { teamTreeData, userList, loading, error, refresh: fetchData };
} 