import { useEffect, useState, useCallback } from 'react'
import { gteClassifyList } from '@/api/roster'
import { ClassifyItem } from '@/views/Roster/component/type'
import { CustomSuccessData } from '@/services/index'

export function useClassifyList() {
  const [data, setData] = useState<ClassifyItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = useCallback(() => {
    setLoading(true)
    setError(null)
    gteClassifyList()
      .then((res: CustomSuccessData<ClassifyItem[]>) => {
        setData(res.data || [])
      })
      .catch(err => {
        setError(err as Error)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refresh: fetchData }
} 