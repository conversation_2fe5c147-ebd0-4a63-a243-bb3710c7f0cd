import React, { useImperativeHandle, forwardRef } from 'react';
import { useEmployeeDetail } from '../hooks/useEmployeeDetail';
import { DynamicForm } from './DynamicForm';
import { useDynamicForm } from '../hooks/useDynamicForm';
import { editUserInfo, removeRecord } from '@/api/roster';
import { DynamicTable } from './DynamicTable';
import { createContext } from 'react';
import { useOrganizationData } from '../hooks/useOrganizationData';

export const TeamTreeContext = createContext<any[]>([]);
export const UserListContext = createContext<{ list: any[]; loading: boolean; error: any }>({ list: [], loading: false, error: null });

interface EmployeeDetailPanelProps {
  userId: number;
  category: string;
  classifyItem: any;
  isEditing: boolean;
  onSave: () => void;
}

export interface EmployeeDetailPanelRef {
  save: () => Promise<void>;
}

const EmployeeDetailPanelInner: React.ForwardRefRenderFunction<EmployeeDetailPanelRef, EmployeeDetailPanelProps> = (
  { userId, category, classifyItem, isEditing },
  ref
) => {
  const { data, loading, error, refresh } = useEmployeeDetail(userId, category);
  const fields = data?.fields || [];
  const record = data?.records?.[0] || {};
  const { formValues, setFormValues, validateForm, getSubmitValues } = useDynamicForm(fields, record.values || {});
  const [_saving, setSaving] = React.useState(false);

  const { teamTreeData, userList, loading: orgDataLoading, error: orgDataError } = useOrganizationData();

  // 副标题切换时重置表单和表格数据
  React.useEffect(() => {
    setFormValues(record.values || {});
    // TODO: 若表格模式，后续可加 setTableRows
  }, [category, record.id]);

  useImperativeHandle(ref, () => ({
    save: async () => {
      if (hasManyRecords) {
        // 表格模式保存逻辑
        setSaving(true);
        try {
          const submitValues = getTableSubmitValues(tableRecords, fields);
          console.log('submitValues', submitValues);
          await editUserInfo({
            id: record.id,
            user_id: userId,
            values: submitValues
          });
          refresh?.();
        } catch (e: any) {
          // window?.alert(e?.message || '保存失败');
          console.log(e);
          throw e;
        } finally {
          setSaving(false);
        }
      } else {
        const valid = validateForm();
        if (!valid.valid) {
          // window?.alert(valid.message);
          console.log(valid.message);
          throw new Error(valid.message);
        }
        setSaving(true);
        try {
          const rawValues = getSubmitValues();
          console.log('rawValues', rawValues);
          const formattedValues: Record<string, any> = {};
          fields.forEach((field: any) => {
            const value = rawValues[field.id];

            // 根据data_type和allow_multiple精细化格式化逻辑
            if (field.data_type === 'Json' || field.allow_multiple) {
              // Json类型或允许复选的字段，API期望数组
              if (Array.isArray(value)) {
                formattedValues[field.id] = value;
              } else if (value !== undefined && value !== null && value !== '') {
                formattedValues[field.id] = [value];
              } else {
                formattedValues[field.id] = []; // 空值设为空数组
              }
            } else {
              // 其他类型且不允许复选的字段，API期望单个值
              if (Array.isArray(value)) {
                formattedValues[field.id] = value.length > 0 ? value[0] : ''; // 取第一个元素，空数组则为空字符串
              } else if (value !== undefined && value !== null) {
                formattedValues[field.id] = value;
              } else {
                formattedValues[field.id] = ''; // 空值设为空字符串
              }
            }
          });
          await editUserInfo({
            id: record.id,
            user_id: userId,
            values: formattedValues,
          });
          refresh?.();
        } catch (e: any) {
          // window?.alert(e?.message || '保存失败');
          console.log(e);
          throw e;
        } finally {
          setSaving(false);
        }
      }
    }
  }));

  // 根据category查找当前子项，判断是否为多条记录（表格模式）
  const currentSubItem = classifyItem?.sub_items?.find((sub: any) => sub.category === category);
  const hasManyRecords = currentSubItem?.has_many_records;
  const tableRecords = data?.records?.length ? data.records : [{ values: {} }];

  // 表格模式下组装提交数据
  const getTableSubmitValues = (rows: any[], fields: any[]) => {
    const result: Record<string, any[]> = {};
    fields.forEach(field => {
      result[field.id] = rows.map(row => {
        const value = row.values[field.id];
        // 无论数据类型，均保证为一维数组
        return value;
      });
    });
    return result;
  };

  if (loading) return <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500">正在加载员工信息...</div>;
  if (error) return <div className="bg-white rounded-lg shadow-sm p-8 text-center text-red-500">加载失败：{error.message}</div>;
  if (!data) return <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500">暂无数据</div>;

  return (
    <TeamTreeContext.Provider value={teamTreeData}>
      <UserListContext.Provider value={{ list: userList, loading: orgDataLoading, error: orgDataError }}>
        <div className="bg-gray-50 min-h-screen">
          {/* 动态表单或表格渲染 */}
          <div className="pb-6">
            {hasManyRecords ? (
              <DynamicTable
                fields={fields}
                records={tableRecords}
                isEditing={isEditing}
                onChange={() => {}}
                onSaveRow={async (row, done) => {
                  setSaving(true);
                  try {
                    const singleRowValues: Record<string, any[]> = {};
                    fields.forEach((field: any) => {
                      singleRowValues[field.id] = Array.isArray(row.values[field.id]) ? row.values[field.id] : [row.values[field.id]];
                    });
                    const req: any = {
                      user_id: userId,
                      values: singleRowValues
                    };
                    if (row.id) req.id = row.id; // 编辑时传id，新增不传
                    await editUserInfo(req);
                    refresh?.();
                    done(true);
                  } catch (e) {
                    console.log(e);
                    done(false);
                  } finally {
                    setSaving(false);
                  }
                }}
                onDeleteRow={async (rowId, done) => {
                  setSaving(true);
                  try {
                    await removeRecord({ id: Number(rowId), user_id: userId, category });
                    refresh?.();
                    done(true);
                  } catch (e) {
                    console.log(e);
                    done(false);
                  } finally {
                    setSaving(false);
                  }
                }}
              />
            ) : (
              <DynamicForm
                fields={fields}
                values={formValues}
                isEditing={isEditing}
                onChange={setFormValues}
              />
            )}
          </div>
          <div className="bg-white rounded-lg shadow-sm border-t px-6 py-3 text-xs text-gray-500 flex justify-between">
            <span>最后更新时间: {data.lastUpdated || '2023年5月6日 16:32分'}</span>
            <span>更新记录</span>
          </div>
        </div>
      </UserListContext.Provider>
    </TeamTreeContext.Provider>
  );
};

export const EmployeeDetailPanel = forwardRef<EmployeeDetailPanelRef, EmployeeDetailPanelProps>(EmployeeDetailPanelInner); 