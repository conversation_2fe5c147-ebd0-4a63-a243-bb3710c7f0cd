import React, { useState } from 'react';
import type { Field } from './DynamicForm';

export interface TableRow {
  id?: number | string;
  values: Record<string, any>;
}

interface DynamicTableProps {
  fields: Field[];
  records: TableRow[];
  isEditing: boolean;
  onChange: (rows: TableRow[]) => void;
  onSaveRow?: (row: TableRow, done: (success: boolean) => void, rowId?: number | string) => void;
  onDeleteRow?: (rowId: number | string, done: (success: boolean) => void) => void;
}

const baseInputClass =
  'block w-full rounded border border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/30 transition bg-white px-3 py-2 text-base leading-tight placeholder-gray-400';
const baseReadClass =
  'block w-full rounded border border-gray-200 bg-gray-50 px-3 py-2 text-base text-gray-700';

export const DynamicTable: React.FC<DynamicTableProps> = ({ fields, records, isEditing, onChange, onSaveRow, onDeleteRow }) => {
  const [rows, setRows] = useState<TableRow[]>(records.length ? records : [{ values: {} }]);
  const [addingRowIdx, setAddingRowIdx] = useState<number | null>(null);
  const [addingRow, setAddingRow] = useState<TableRow | null>(null);
  const [saving, setSaving] = useState(false);
  const [editingRowIdx, setEditingRowIdx] = useState<number | null>(null);

  // 行内编辑
  const handleCellChange = (rowIdx: number, fieldId: string, value: any) => {
    const newRows = rows.map((row, idx) =>
      idx === rowIdx ? { ...row, values: { ...row.values, [fieldId]: value } } : row
    );
    setRows(newRows);
    onChange(newRows);
  };

  // 添加行
  const handleAddRow = () => {
    setAddingRow({ values: {} });
    setAddingRowIdx(rows.length);
  };

  // 保存新行
  const handleSaveRow = () => {
    if (!addingRow) return;
    setSaving(true);
    onSaveRow && onSaveRow(addingRow, (success: boolean) => {
      if (success) {
        const newRows = [...rows, addingRow];
        setRows(newRows);
        onChange(newRows);
        setAddingRow(null);
        setAddingRowIdx(null);
      }
      setSaving(false);
    });
  };

  // 取消新行
  const handleCancelRow = () => {
    setAddingRow(null);
    setAddingRowIdx(null);
  };

  // 删除行
  const handleDeleteRow = (idx: number) => {
    const row = rows[idx];
    if (row.id && onDeleteRow) {
      setSaving(true);
      onDeleteRow(row.id, (success: boolean) => {
        if (success) {
          const newRows = rows.filter((_, i) => i !== idx);
          setRows(newRows);
          onChange(newRows);
        }
        setSaving(false);
      });
    } else {
      // 新建行本地删除
      const newRows = rows.filter((_, i) => i !== idx);
      setRows(newRows);
      onChange(newRows);
    }
  };

  // 行级保存
  const handleSaveEditRow = (rowIdx: number) => {
    setSaving(true);
    onSaveRow && onSaveRow(rows[rowIdx], (success: boolean) => {
      if (success) {
        setEditingRowIdx(null);
      }
      setSaving(false);
    }, rows[rowIdx].id);
  };

  // 行级进入编辑
  const handleEditRow = (rowIdx: number) => {
    setEditingRowIdx(rowIdx);
  };

  // 只读态显示
  const renderCell = (row: TableRow, field: Field) => {
    const value = row.values[field.id] ?? '';
    return (
      <div className={baseReadClass}>{value ? value : <span className="text-gray-400">未填写</span>}</div>
    );
  };

  // 编辑态输入
  const renderInput = (rowIdx: number, row: TableRow, field: Field, isNewRow = false) => {
    const value = row.values[field.id] ?? '';
    const onChangeFn = isNewRow
      ? (v: any) => setAddingRow(r => ({ ...r!, values: { ...r!.values, [field.id]: v } }))
      : (v: any) => handleCellChange(rowIdx, field.id, v);
    switch (field.field_type) {
      case 'SingleLineText':
        return (
          <input
            type="text"
            className={baseInputClass}
            value={value}
            maxLength={field.length}
            onChange={e => onChangeFn(e.target.value)}
          />
        );
      case 'MultilineText':
        return (
          <textarea
            className={baseInputClass}
            value={value}
            maxLength={field.length}
            onChange={e => onChangeFn(e.target.value)}
          />
        );
      case 'Date':
        return (
          <input
            type="date"
            className={baseInputClass}
            value={value}
            onChange={e => onChangeFn(e.target.value)}
          />
        );
      case 'DateTime':
        return (
          <input
            type="datetime-local"
            className={baseInputClass}
            value={value}
            onChange={e => onChangeFn(e.target.value)}
          />
        );
      case 'Number':
        return (
          <input
            type="number"
            className={baseInputClass}
            value={value}
            onChange={e => onChangeFn(e.target.value)}
          />
        );
      case 'CustomSelector':
        return (
          <select
            className={baseInputClass}
            value={value}
            onChange={e => onChangeFn(e.target.value)}
          >
            <option value="">请选择</option>
            {field.options?.map(opt => (
              <option key={opt} value={opt}>{opt}</option>
            ))}
          </select>
        );
      case 'Sex':
        return (
          <select
            className={baseInputClass}
            value={value}
            onChange={e => onChangeFn(e.target.value)}
          >
            <option value="">请选择</option>
            <option value="male">男</option>
            <option value="female">女</option>
          </select>
        );
      default:
        return (
          <input
            type="text"
            className={baseInputClass}
            value={value}
            onChange={e => onChangeFn(e.target.value)}
          />
        );
    }
  };

  return (
    <div className="overflow-x-auto bg-white rounded-xl shadow-md p-4 md:p-8 mx-auto">
      <div className="min-w-max">
        <table className="min-w-full border-separate border-spacing-y-2 table-fixed">
          <thead>
            <tr>
              {fields.map(field => (
                <th key={field.id} className="px-3 py-2 text-left text-gray-700 font-medium bg-gray-50 border-b border-gray-200 min-w-[150px] whitespace-nowrap">
                  {field.field_name}
                </th>
              ))}
              {isEditing && <th className="px-3 py-2 bg-gray-50 border-b border-gray-200 w-24">操作</th>}
            </tr>
          </thead>
          <tbody>
            {rows.map((row, rowIdx) => (
              <tr key={row.id ?? rowIdx} className="align-top">
                {fields.map(field => (
                  <td key={field.id} className="px-3 py-2 align-middle min-w-[150px] whitespace-nowrap">
                    {isEditing && editingRowIdx === rowIdx
                      ? renderInput(rowIdx, row, field)
                      : renderCell(row, field)}
                  </td>
                ))}
                {isEditing && (
                  <td className="px-3 py-2 align-middle text-center flex flex-wrap">
                    {editingRowIdx === rowIdx ? (
                      <button
                        type="button"
                        className="text-green-600 hover:underline text-sm mr-2 whitespace-nowrap"
                        onClick={() => handleSaveEditRow(rowIdx)}
                        disabled={saving}
                      >
                        保存
                      </button>
                    ) : (
                      <button
                        type="button"
                        className="text-blue-600 hover:underline text-sm mr-2 whitespace-nowrap"
                        onClick={() => handleEditRow(rowIdx)}
                        disabled={saving}
                      >
                        编辑
                      </button>
                    )}
                    <button
                      type="button"
                      className="text-red-500 hover:underline text-sm whitespace-nowrap"
                      onClick={() => handleDeleteRow(rowIdx)}
                      disabled={rows.length === 1}
                    >
                      删除
                    </button>
                  </td>
                )}
              </tr>
            ))}
            {/* 新建行 */}
            {isEditing && addingRow && (
              <tr className="align-top">
                {fields.map(field => (
                  <td key={field.id} className="px-3 py-2 align-middle flex-1 overflow-hidden">
                    {renderInput(addingRowIdx!, addingRow, field, true)}
                  </td>
                ))}
                <td className="px-3 py-2 align-middle text-center flex flex-wrap">
                  <button
                    type="button"
                    className="text-green-600 hover:underline text-sm mr-2 whitespace-nowrap"
                    onClick={handleSaveRow}
                    disabled={saving}
                  >
                    保存
                  </button>
                  <button
                    type="button"
                    className="text-gray-400 hover:underline text-sm whitespace-nowrap"
                    onClick={handleCancelRow}
                    disabled={saving}
                  >
                    取消
                  </button>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {isEditing && !addingRow && (
          <div className="mt-4 flex justify-end w-full">
            <button
              type="button"
              className="px-4 py-2 bg-primary text-white rounded shadow hover:bg-primary/90 transition"
              onClick={handleAddRow}
            >
              添加行
            </button>
          </div>
        )}
      </div>
    </div>
  );
}; 