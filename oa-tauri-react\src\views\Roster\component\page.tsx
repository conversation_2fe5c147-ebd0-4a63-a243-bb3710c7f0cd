import { useEffect, useState } from 'react';
import { DataTable } from './data-table';
import { columns } from './columns';



export default function ConentPage() {
    const [data, setData] = useState(null);

    useEffect(() => {
        async function fetchData() {
            const result: any = await getData();
            setData(result);
        }
        fetchData();
    }, []);

    if (!data) {
        return <div>Loading...</div>;
    }

    return (
        <div className="mx-auto ">

            <DataTable columns={columns} data={data} />
        </div>
    );
}

async function getData() {
    return [
      {
        index: 1,
        name: "张三",
        jobNumber: "10023",
        gender: "男",
        age: "30",
        status: "在职",
        department: "广州总部/部门1",
        position: "前端开发",
        idCard: "440***********1234",
        phone: "138****5678",
        salary: "¥8000",
        departmentId: "dept1"
      },
      {
        index: 2,
        name: "李四",
        jobNumber: "10024",
        gender: "男",
        age: "28",
        status: "在职",
        department: "广州总部/部门2",
        position: "前端开发",
        idCard: "440***********5678",
        phone: "139****1234",
        salary: "¥8000",
        departmentId: "dept2"
      },
      {
        index: 3,
        name: "王五",
        jobNumber: "10025",
        gender: "男",
        age: "32",
        status: "在职",
        department: "广州总部/部门3",
        position: "前端开发",
        idCard: "440***********9012",
        phone: "137****5678",
        salary: "¥8000",
        departmentId: "dept3"
      }
    ];
}
