import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface RosterTableProps {
  data: any[]
}

export function RosterTable({ data }: RosterTableProps) {
  const columns = [
    { id: "index", header: "序号" },
    { id: "name", header: "姓名" },
    { id: "jobNumber", header: "工号/工牌" },
    { id: "gender", header: "性别" },
    { id: "age", header: "年龄" },
    { id: "status", header: "状态" },
    { id: "department", header: "所属部门" },
    { id: "position", header: "工作岗位" },
    { id: "entryDate", header: "入职日期" },
    { id: "contractDate", header: "合同日期" },
    { id: "idCard", header: "身份证" },
    { id: "phone", header: "电话" },
    { id: "address", header: "地址" },
    { id: "education", header: "学历" },
    { id: "major", header: "专业" },
    { id: "bank", header: "银行卡号" },
    { id: "salary", header: "工资" },
    { id: "socialSecurity", header: "社保" },
    { id: "accumulationFund", header: "公积金" },
    { id: "taxNumber", header: "税号" },
    { id: "emergencyContact", header: "紧急联系人" },
    { id: "remarks", header: "备注" },
  ]

  return (
    <div className="w-full overflow-auto">
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.id} className="whitespace-nowrap">
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((row, rowIndex) => (
            <TableRow key={rowIndex}>
              {columns.map((column) => (
                <TableCell key={`${rowIndex}-${column.id}`} className="whitespace-nowrap">
                  {row[column.id] || "-"}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}