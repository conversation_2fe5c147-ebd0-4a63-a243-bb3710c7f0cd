import { useState } from 'react';
import type { Field, FormValues } from '../components/DynamicForm';

export function useDynamicForm(fields: Field[], initialValues: FormValues = {}) {
  const [formValues, setFormValues] = useState<FormValues>(() => {
    // 初始化表单值，优先用initialValues，否则默认空
    const values: FormValues = {};
    fields.forEach(field => {
      values[field.id] = initialValues[field.id] ?? '';
    });
    return values;
  });

  // 单个字段赋值
  const setFormValue = (id: string, value: any) => {
    setFormValues(prev => ({ ...prev, [id]: value }));
  };

  // 校验必填项
  const validateForm = () => {
    for (const field of fields) {
      if (field.is_required && !formValues[field.id]) {
        return { valid: false, message: `${field.field_name}为必填项` };
      }
    }
    return { valid: true };
  };

  // 获取提交用的values（可按后端要求格式化）
  const getSubmitValues = () => {
    return { ...formValues };
  };

  return {
    formValues,
    setFormValue,
    setFormValues,
    validateForm,
    getSubmitValues,
  };
} 