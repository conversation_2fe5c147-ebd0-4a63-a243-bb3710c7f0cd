import React from 'react'
import { cn } from '@/lib/utils'
import { CalendarDay } from '../hooks/useCalendar'

interface CalendarGridProps {
  weekDays: string[]
  days: CalendarDay[]
  onDayClick?: (day: CalendarDay) => void
  className?: string
}

interface DayCardProps {
  day: CalendarDay
  onClick?: (day: CalendarDay) => void
}

const DayCard: React.FC<DayCardProps> = ({ day, onClick }) => {
  return (
    <div
      className={cn(
        "relative min-h-[80px] sm:min-h-[100px] md:min-h-[120px] p-2 sm:p-3 border-r border-b last:border-r-0 cursor-pointer transition-all duration-200",
        "hover:bg-blue-50 hover:shadow-md",
        day.isCurrentMonth
          ? "bg-white"
          : "bg-gray-50/50",
        day.isToday
          ? "bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200"
          : "border-gray-100"
      )}
      onClick={() => onClick?.(day)}
    >
      <div className="flex flex-col h-full">
        {/* 日期头部 */}
        <div className="flex items-center justify-between mb-1 sm:mb-2">
          <div className={cn(
            "flex items-center gap-1 sm:gap-2",
            day.isCurrentMonth ? "text-gray-900" : "text-gray-400"
          )}>
            {day.isToday ? (
              <div className="flex items-center gap-1 sm:gap-2">
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs sm:text-sm font-bold shadow-lg">
                  {day.date}
                </div>
                <span className="text-xs font-medium text-blue-600 hidden sm:inline">今天</span>
              </div>
            ) : (
              <span className={cn(
                "text-sm sm:text-base md:text-lg font-medium",
                day.isToday && "font-bold text-blue-600"
              )}>
                {day.date}
              </span>
            )}
          </div>
          <span className={cn(
            "text-xs hidden sm:inline",
            day.isCurrentMonth ? "text-gray-500" : "text-gray-400"
          )}>
            {day.chineseNumber}
          </span>
        </div>
        
        {/* 事件区域 */}
        <div className="flex-1 space-y-0.5 sm:space-y-1">
          {/* 示例事件 - 可以根据实际数据渲染 */}
          {day.isToday && (
            <div className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md truncate">
              今日任务
            </div>
          )}
          {day.date === 15 && day.isCurrentMonth && (
            <div className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md truncate">
              会议
            </div>
          )}
          {day.date === 20 && day.isCurrentMonth && (
            <div className="text-xs bg-purple-100 text-purple-700 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md truncate">
              培训
            </div>
          )}
        </div>
        
        {/* 悬浮效果 */}
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/0 to-purple-500/0 hover:from-blue-500/5 hover:to-purple-500/5 transition-all duration-200 pointer-events-none" />
      </div>
    </div>
  )
}

export const CalendarGrid: React.FC<CalendarGridProps> = ({
  weekDays,
  days,
  onDayClick,
  className
}) => {
  return (
    <div className={cn("bg-white rounded-lg md:rounded-xl shadow-sm border border-gray-100 overflow-hidden", className)}>
      {/* 星期标题 */}
      <div className="grid grid-cols-7 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        {weekDays.map((day, index) => (
          <div
            key={index}
            className="p-2 sm:p-3 md:p-4 text-center border-r last:border-r-0 border-gray-200"
          >
            <span className="text-xs sm:text-sm font-semibold text-gray-700">{day}</span>
          </div>
        ))}
      </div>
      
      {/* 日历网格 */}
      <div className="grid grid-cols-7 relative">
        {days.map((day, index) => (
          <DayCard
            key={index}
            day={day}
            onClick={onDayClick}
          />
        ))}
      </div>
    </div>
  )
}
