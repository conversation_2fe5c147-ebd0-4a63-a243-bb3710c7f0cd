import { emit } from '@tauri-apps/api/event'
import { WebviewWindow } from '@tauri-apps/api/webviewWindow'

interface OpenWindowOptions {
  label: string                  // 窗口唯一标识
  url: string                   // 路由地址
  width?: number                // 宽度，可选
  height?: number               // 高度，可选
  onCreated?: () => void        // 创建成功回调，可选
  onError?: (e: any) => void    // 创建失败回调，可选
  initData?: any                // 初始化要传给子窗口的数据，可选
}
export function openWindow({
  label,
  url,
  width = 600,
  height = 400,
  onCreated,
  onError,
  initData,
}: OpenWindowOptions) {
  const win = new WebviewWindow(label, {
    url,
    width,
    height,
  })

  win.once('tauri://created', () => {
    onCreated?.()
    // 如果有初始化数据，发送给子窗口
    if (initData !== undefined) {
      // 向所有窗口广播数据，子窗口监听这个事件
      emit('init-data', initData);
    }
  })

  win.once('tauri://error', (e) => {
    onError?.(e)
  })

  return win
}
