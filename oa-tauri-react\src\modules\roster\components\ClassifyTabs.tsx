import React from 'react';
import { Tabs,  TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ClassifyItem } from '../types/classify';
import { Button } from "@/components/ui/button";

interface ClassifyTabsProps {
  activeTab: string;
  onSelectCategoryTab: (tab: string) => void;
  classifyItem: ClassifyItem;
  isEditing: boolean;
  saving: boolean;
  onEdit: () => void;
  onSave: () => void;
  showSaveButton?: boolean;
  hideWhenEditing?: boolean;
}

export const ClassifyTabs: React.FC<ClassifyTabsProps> = ({ activeTab, onSelectCategoryTab, classifyItem, isEditing, saving, onEdit, onSave, showSaveButton, hideWhenEditing }) => {
  if (!classifyItem || !classifyItem.sub_items?.length) return null;
  const subItems = classifyItem.sub_items;
  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex-1">
        <Tabs value={activeTab} onValueChange={onSelectCategoryTab} className={`w-full flex mb-6`}>
          <TabsList className={`mb-4`}>
            {subItems.map(sub => (
              <TabsTrigger value={sub.category} key={sub.category} className={`w-full`}>
                {sub.description}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
      <div className="ml-4">
        {activeTab === 'FamilyInfo' ? (
          !isEditing ? (
            <Button variant="outline" size="sm" onClick={onEdit}>
              编辑
            </Button>
          ) : (
            <Button variant="default" size="sm" onClick={onEdit}>
              退出编辑
            </Button>
          )
        ) : (
          !hideWhenEditing && (
            isEditing && showSaveButton ? (
              <Button variant="default" size="sm" onClick={onSave} disabled={saving}>
                {saving ? '保存中...' : '保存'}
              </Button>
            ) : (
              <Button variant="outline" size="sm" onClick={onEdit}>
                编辑
              </Button>
            )
          )
        )}
      </div>
    </div>
  );
};