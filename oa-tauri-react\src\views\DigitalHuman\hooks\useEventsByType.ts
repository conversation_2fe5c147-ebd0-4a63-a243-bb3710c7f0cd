import { useState, useMemo } from 'react'
import { CalendarEventData } from '../components/CalendarEvent'

export type EventType = 'calendar' | 'attendance' | 'leave' | 'holiday' | 'overtime' | 'task' | 'punishment' | 'meal'

// 示例事件数据 - 按事项类型分类
const defaultEventsByType: Record<EventType, CalendarEventData[]> = {
  calendar: [
    {
      id: 'cal-1',
      title: '学习计划制定',
      startDate: 11,
      endDate: 11,
      startTime: '8:30',
      endTime: '17:30',
      location: '1#小时',
      organizer: '举办单位',
      eventType: 'calendar'
    },
    {
      id: 'cal-2',
      title: '团队会议',
      startDate: 15,
      endDate: 15,
      startTime: '14:00',
      endTime: '16:00',
      location: '会议室A',
      eventType: 'calendar'
    }
  ],
  attendance: [
    {
      id: 'att-1',
      title: '正常考勤',
      startDate: 1,
      endDate: 30,
      startTime: '09:00',
      endTime: '18:00',
      eventType: 'attendance',
      description: '每日正常打卡'
    }
  ],
  leave: [
    {
      id: 'leave-1',
      title: '年假',
      startDate: 8,
      endDate: 10,
      eventType: 'leave',
      description: '年假休息'
    },
    {
      id: 'leave-2',
      title: '病假',
      startDate: 22,
      endDate: 23,
      eventType: 'leave',
      description: '感冒请假'
    }
  ],
  holiday: [
    {
      id: 'holiday-1',
      title: '春节假期',
      startDate: 1,
      endDate: 7,
      eventType: 'holiday',
      description: '法定节假日'
    },
    {
      id: 'holiday-2',
      title: '清明节',
      startDate: 4,
      endDate: 6,
      eventType: 'holiday',
      description: '清明节假期'
    }
  ],
  overtime: [
    {
      id: 'overtime-1',
      title: '周末加班',
      startDate: 5,
      endDate: 6,
      startTime: '09:00',
      endTime: '17:00',
      eventType: 'overtime',
      description: '项目赶工'
    },
    {
      id: 'overtime-2',
      title: '调休',
      startDate: 18,
      endDate: 19,
      eventType: 'overtime',
      description: '补偿性调休'
    }
  ],
  task: [
    {
      id: 'task-1',
      title: 'T001-开发任务',
      startDate: 12,
      endDate: 16,
      eventType: 'task',
      description: '前端开发任务'
    },
    {
      id: 'task-2',
      title: 'T002-测试任务',
      startDate: 20,
      endDate: 25,
      eventType: 'task',
      description: '系统测试任务'
    }
  ],
  punishment: [
    {
      id: 'punishment-1',
      title: '迟到处罚',
      startDate: 14,
      endDate: 14,
      eventType: 'punishment',
      description: '迟到15分钟'
    }
  ],
  meal: [
    {
      id: 'meal-1',
      title: '工作餐',
      startDate: 1,
      endDate: 30,
      eventType: 'meal',
      description: '每日工作餐'
    }
  ]
}

export const useEventsByType = () => {
  const [activeEventType, setActiveEventType] = useState<EventType>('calendar')
  const [eventsByType, setEventsByType] = useState(defaultEventsByType)
  
  // 获取当前激活类型的事件
  const activeEvents = useMemo(() => {
    return eventsByType[activeEventType] || []
  }, [eventsByType, activeEventType])
  
  // 获取指定日期的事件
  const getEventsForDate = (date: number): CalendarEventData[] => {
    return activeEvents.filter(event => 
      date >= event.startDate && date <= event.endDate
    )
  }
  
  // 获取跨列事件
  const getSpanningEvents = (): CalendarEventData[] => {
    return activeEvents.filter(event => 
      event.startDate !== event.endDate
    )
  }
  
  // 获取单日事件
  const getSingleDayEvents = (): CalendarEventData[] => {
    return activeEvents.filter(event => 
      event.startDate === event.endDate
    )
  }
  
  // 添加事件
  const addEvent = (eventType: EventType, event: CalendarEventData) => {
    setEventsByType(prev => ({
      ...prev,
      [eventType]: [...prev[eventType], event]
    }))
  }
  
  // 删除事件
  const removeEvent = (eventType: EventType, eventId: string) => {
    setEventsByType(prev => ({
      ...prev,
      [eventType]: prev[eventType].filter(event => event.id !== eventId)
    }))
  }
  
  // 更新事件
  const updateEvent = (eventType: EventType, eventId: string, updatedEvent: Partial<CalendarEventData>) => {
    setEventsByType(prev => ({
      ...prev,
      [eventType]: prev[eventType].map(event => 
        event.id === eventId ? { ...event, ...updatedEvent } : event
      )
    }))
  }
  
  // 获取事件统计
  const getEventStats = useMemo(() => {
    const stats: Record<EventType, number> = {
      calendar: 0,
      attendance: 0,
      leave: 0,
      holiday: 0,
      overtime: 0,
      task: 0,
      punishment: 0,
      meal: 0
    }
    
    Object.entries(eventsByType).forEach(([type, events]) => {
      stats[type as EventType] = events.length
    })
    
    return stats
  }, [eventsByType])
  
  return {
    activeEventType,
    setActiveEventType,
    activeEvents,
    getEventsForDate,
    getSpanningEvents,
    getSingleDayEvents,
    addEvent,
    removeEvent,
    updateEvent,
    getEventStats,
    eventsByType
  }
}
