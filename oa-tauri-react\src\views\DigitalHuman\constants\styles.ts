// 数字人日历页面样式常量

export const COLORS = {
  // 主色调
  primary: {
    50: 'from-blue-50 to-purple-50',
    100: 'from-blue-100 to-purple-100',
    500: 'from-blue-500 to-purple-500',
    600: 'from-blue-600 to-purple-600',
  },
  
  // 功能模块颜色
  modules: {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
    indigo: 'bg-indigo-500',
    pink: 'bg-pink-500',
    teal: 'bg-teal-500',
    cyan: 'bg-cyan-500',
  },
  
  // 状态颜色
  states: {
    today: 'bg-gradient-to-r from-blue-500 to-purple-500',
    hover: 'hover:bg-blue-50',
    active: 'bg-blue-100',
    disabled: 'bg-gray-100 text-gray-400',
  }
}

export const SHADOWS = {
  card: 'shadow-sm hover:shadow-lg',
  button: 'shadow-md hover:shadow-lg',
  calendar: 'shadow-sm',
}

export const TRANSITIONS = {
  default: 'transition-all duration-200',
  slow: 'transition-all duration-300',
  fast: 'transition-all duration-150',
}

export const SPACING = {
  cardPadding: 'p-6',
  buttonPadding: 'px-4 py-2',
  sectionGap: 'space-y-6',
}

export const BORDERS = {
  card: 'border border-gray-100',
  input: 'border border-gray-200',
  active: 'border-blue-200',
}

export const TYPOGRAPHY = {
  title: 'text-2xl font-bold text-gray-900',
  subtitle: 'text-xl font-bold text-gray-900',
  body: 'text-sm text-gray-700',
  caption: 'text-xs text-gray-500',
  button: 'text-sm font-medium',
}
