import * as React from "react"
import { cn } from "@/lib/utils"
import { useMenuNavigation, MenuItemConfig } from "./useMenuNavigation"
import { ClipboardList, Users, Banknote, Building, BarChart2, FileText, Megaphone } from "lucide-react"

interface MenuItemProps {
    icon: React.ReactNode
    label: string
    onClick?: () => void
    className?: string
}

function MenuItem({ icon, label, onClick, className }: MenuItemProps) {
    return (
        <div
            className={cn(
                "bg-white p-4 rounded shadow cursor-pointer hover:shadow-md transition-shadow flex items-center gap-2 w-full hover:bg-gray-100",
                className
            )}
            onClick={onClick}
        >
            <div className="bg-[#f7b51c] text-xl rounded text-white p-2">{icon}</div>
            <span className="text-sm flex-1">{label}</span>
        </div>
    )
}

interface ManagementMenuProps {
    className?: string
}

export function ManagementMenu({ className }: ManagementMenuProps) {

    
    const rawMenuItems: MenuItemConfig[] = [
        { icon: <ClipboardList />, label: "行政管理", path: "/admnstrtn" },
        { icon: <Users />, label: "人力资源管理", path: "/roster/dept2" },
        { icon: <Banknote />, label: "财务管理", path: "/roster/dept3" },
        { icon: <Building />, label: "设备管理" },
        { icon: <BarChart2 />, label: "总务管理", path: "/employee/info" },
        { icon: <FileText />, label: "报表管理" },
        { icon: <Megaphone />, label: "公告管理" }
    ]
    const menuItems = useMenuNavigation(rawMenuItems)

    return (
        <div className={cn("grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4", className)}>
            {menuItems.map((item, index) => (
                <MenuItem
                    key={index}
                    icon={item.icon}
                    label={item.label}
                    onClick={item.onClick}
                />
            ))}
        </div>
    )
}
