import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search,  Download} from "lucide-react"

interface RosterToolbarProps {
  onSearch: (query: string) => void
  onAdd: () => void
  onExport: () => void
  onImport: () => void
  onFilter: () => void
}

export function RosterToolbar({ onSearch,  onExport }: RosterToolbarProps) {
  const [searchQuery, setSearchQuery] = React.useState("")

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchQuery)
  }

  return (
    <div className="flex items-center justify-between p-4 bg-white border-b">
      <div className="flex items-center space-x-2">
        <form onSubmit={handleSearch} className="flex container items-center">
          <Input
            placeholder="搜索员工信息..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-64"
          />
          <Button type="submit" variant="ghost" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </form>
        {/* <Button variant="outline" size="sm" onClick={onFilter}>
          <Filter className="h-4 w-4 mr-2" />
          筛选
        </Button> */}
      </div>
      <div className="flex items-center space-x-2">
        {/* <Button size="sm" onClick={onAdd}>
          <Plus className="h-4 w-4 mr-2" />
          新增员工
        </Button>
        <Button variant="outline" size="sm" onClick={onImport}>
          <Upload className="h-4 w-4 mr-2" />
          导入
        </Button> */}
        <Button variant="outline" size="sm" onClick={onExport}>
          <Download className="h-4 w-4 mr-2" />
          导出
        </Button>
      </div>
    </div>
  )
}