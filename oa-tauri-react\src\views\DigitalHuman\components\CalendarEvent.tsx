import React, { useState } from 'react'
import { cn } from '@/lib/utils'

export interface CalendarEventData {
  id: string
  title: string
  startDate: number // 开始日期
  endDate: number   // 结束日期
  startTime?: string
  endTime?: string
  location?: string
  organizer?: string
  eventType: 'calendar' | 'attendance' | 'leave' | 'holiday' | 'overtime' | 'task' | 'punishment' | 'meal'
  color?: string
  description?: string
}

interface CalendarEventProps {
  event: CalendarEventData
  className?: string
}

const eventColors = {
  calendar: 'bg-blue-500',
  attendance: 'bg-green-500',
  leave: 'bg-red-500',
  holiday: 'bg-purple-500',
  overtime: 'bg-orange-500',
  task: 'bg-indigo-500',
  punishment: 'bg-gray-500',
  meal: 'bg-pink-500'
}

export const CalendarEvent: React.FC<CalendarEventProps> = ({ event, className }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const eventColor = event.color || eventColors[event.eventType]

  // 构建完整的事件描述
  const fullDescription = [
    event.title,
    event.startTime && event.endTime && `${event.startTime} - ${event.endTime}`,
    event.location && `地点：${event.location}`,
    event.organizer && `举办单位：${event.organizer}`,
    event.description
  ].filter(Boolean).join(' ')

  // 截断文本用于显示
  const truncatedText = fullDescription.length > 30
    ? `${fullDescription.substring(0, 30)}...`
    : fullDescription

  const displayText = isExpanded ? fullDescription : truncatedText

  return (
    <div
      className={cn(
        "text-xs text-white px-2 py-1 rounded text-center font-medium shadow-sm cursor-pointer transition-all duration-200 hover:shadow-md",
        eventColor,
        className
      )}
      onClick={() => setIsExpanded(!isExpanded)}
      title={fullDescription} // 悬浮提示显示完整内容
    >
      {displayText}
    </div>
  )
}

// 示例事件数据
export const sampleEvents: Record<number, CalendarEventData[]> = {
  11: [
    {
      id: '1',
      title: '学习计划制定',
      startTime: '2025年4月11日 8:30',
      endTime: '2025年4月11日 17:30',
      location: '1#小时',
      organizer: '举办单位',
      type: 'meeting'
    }
  ],
  15: [
    {
      id: '2', 
      title: '团队会议',
      startTime: '14:00',
      endTime: '16:00',
      location: '会议室A',
      type: 'meeting'
    }
  ],
  20: [
    {
      id: '3',
      title: '技能培训',
      startTime: '09:00', 
      endTime: '17:00',
      location: '培训中心',
      type: 'training'
    }
  ]
}
