import React, { useState } from 'react'
import { cn } from '@/lib/utils'

export interface CalendarEventData {
  id: string
  title: string
  startDate: number // 开始日期
  endDate: number   // 结束日期
  startTime?: string
  endTime?: string
  location?: string
  organizer?: string
  eventType: 'calendar' | 'attendance' | 'leave' | 'holiday' | 'overtime' | 'task' | 'punishment' | 'meal'
  color?: string
  description?: string
}

interface CalendarEventProps {
  event: CalendarEventData
  currentDate: number // 当前日期格子的日期
  className?: string
}

const eventColors = {
  calendar: 'bg-blue-500',
  attendance: 'bg-green-500',
  leave: 'bg-red-500',
  holiday: 'bg-purple-500',
  overtime: 'bg-orange-500',
  task: 'bg-indigo-500',
  punishment: 'bg-gray-500',
  meal: 'bg-pink-500'
}

export const CalendarEvent: React.FC<CalendarEventProps> = ({ event, currentDate, className }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const eventColor = event.color || eventColors[event.eventType]

  // 判断当前日期在事件中的位置
  const isStartDate = currentDate === event.startDate
  const isEndDate = currentDate === event.endDate
  const isMiddleDate = currentDate > event.startDate && currentDate < event.endDate
  const isSingleDay = event.startDate === event.endDate

  // 根据位置决定显示的文本
  let displayText = event.title
  if (isSingleDay) {
    // 单日事件显示完整信息
    const fullDescription = [
      event.title,
      event.startTime && event.endTime && `${event.startTime} - ${event.endTime}`,
      event.location && `地点：${event.location}`,
      event.organizer && `举办单位：${event.organizer}`,
      event.description
    ].filter(Boolean).join(' ')

    const truncatedText = fullDescription.length > 30
      ? `${fullDescription.substring(0, 30)}...`
      : fullDescription

    displayText = isExpanded ? fullDescription : truncatedText
  } else {
    // 跨日事件根据位置显示不同内容
    if (isStartDate) {
      displayText = `${event.title} (${event.startDate}-${event.endDate}日)`
    } else if (isEndDate) {
      displayText = `${event.title} (结束)`
    } else if (isMiddleDate) {
      displayText = `${event.title} (进行中)`
    }
  }

  // 根据位置决定样式
  const positionStyles = cn(
    "text-xs text-white px-2 py-1 font-medium shadow-sm cursor-pointer transition-all duration-200 hover:shadow-md",
    eventColor,
    {
      // 单日事件：完整圆角
      "rounded": isSingleDay,
      // 开始日期：左圆角
      "rounded-l": isStartDate && !isSingleDay,
      // 结束日期：右圆角
      "rounded-r": isEndDate && !isSingleDay,
      // 中间日期：无圆角
      "": isMiddleDate,
      // 文字对齐
      "text-center": isSingleDay,
      "text-left": isStartDate && !isSingleDay,
      "text-center": isMiddleDate,
      "text-right": isEndDate && !isSingleDay,
    }
  )

  return (
    <div
      className={cn(positionStyles, className)}
      onClick={() => setIsExpanded(!isExpanded)}
      title={`${event.title} (${event.startDate}-${event.endDate}日)`}
    >
      {displayText}
    </div>
  )
}

// 示例事件数据
export const sampleEvents: Record<number, CalendarEventData[]> = {
  11: [
    {
      id: '1',
      title: '学习计划制定',
      startTime: '2025年4月11日 8:30',
      endTime: '2025年4月11日 17:30',
      location: '1#小时',
      organizer: '举办单位',
      type: 'meeting'
    }
  ],
  15: [
    {
      id: '2', 
      title: '团队会议',
      startTime: '14:00',
      endTime: '16:00',
      location: '会议室A',
      type: 'meeting'
    }
  ],
  20: [
    {
      id: '3',
      title: '技能培训',
      startTime: '09:00', 
      endTime: '17:00',
      location: '培训中心',
      type: 'training'
    }
  ]
}
