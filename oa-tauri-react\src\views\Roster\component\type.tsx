import { Column, ColumnDef, Table } from "@tanstack/react-table"


export interface DataTableViewOptionsProps<TData> {
    table: Table<TData>
}

export interface DataTableColumnHeaderProps<TData, TValue>
    extends React.HTMLAttributes<HTMLDivElement> {
    column: Column<TData, TValue>
    title: string
}

export interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[]
    data: TData[]
}

export type Employee = {
    index: number
    name: string
    jobNumber: string
    gender: string
    age: string
    status: string
    department: string
    position: string
    entryDate?: string
    contractDate?: string
    idCard: string
    phone: string
    address?: string
    education?: string
    major?: string
    bank?: string
    salary: string
    socialSecurity?: string
    accumulationFund?: string
    taxNumber?: string
    emergencyContact?: string
    remarks?: string
    departmentId: string
    birthDate?: string;
    lunarBirthday?: string;
    ethnicity?: string;
    nativePlace?: string;
    contactMethod?: string;
    email?: string;
    idNumber?: string;
    maritalStatus?: string;
    politicalStatus?: string;
    degree?: string;
    languageSkills?: string;
    currentAddress?: string;
    healthStatus?: string;
    idCardFront?: string;
    idCardBack?: string;
    hireDate?: string;
    workStatus?: string;
    contractType?: string;
    contractTerm?: string;
    relationship?: string;
    emergencyContactNumber?: string;
    familyMembers?: any[];
    workExperiences?: any[];
    educationExperiences?: any[];
    trainingExperiences?: any[];
    awards?: any[];
    punishments?: any[];
    performanceReviews?: any[];
    salaryChanges?: any[];
    housingFund?: any;
    commercialInsurance?: any;
    title?: string;
    leaveDate?: string;
    leaveReason?: string;
    rehireDate?: string;
    rehireReason?: string;
    otherNotes?: string;
}

export interface TeamListItem {
  avatar: string | null;
  id: number;
  job: string;
  name: string;
  pid: number;
  real_name: string;
  side: string | null;
  type: string;
}

export interface SubItem {
  category: string;
  description: string;
  has_many_records: boolean;
}

export interface ClassifyItem {
  classify: string;
  description: string;
  writable?: boolean;
  sub_items: SubItem[];
}


