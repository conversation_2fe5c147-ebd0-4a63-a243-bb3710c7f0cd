# 多事项类型日历系统

## 功能概述

实现了一个支持多种事项类型切换和跨列显示的日历系统，用户可以通过顶部按钮切换不同的事项类型，查看对应的日程安排。

## 事项类型

### 📋 支持的事项类型
1. **日历** - 普通日程安排
2. **考勤打卡** - 考勤记录
3. **请假** - 请假申请
4. **假期** - 法定节假日
5. **调休** - 加班调休
6. **任务码** - 工作任务
7. **处罚** - 违规处罚
8. **报餐** - 用餐安排

### 🎨 颜色编码
```tsx
const eventColors = {
  calendar: 'bg-blue-500',      // 日历 - 蓝色
  attendance: 'bg-green-500',   // 考勤打卡 - 绿色
  leave: 'bg-red-500',          // 请假 - 红色
  holiday: 'bg-purple-500',     // 假期 - 紫色
  overtime: 'bg-orange-500',    // 调休 - 橙色
  task: 'bg-indigo-500',        // 任务码 - 靛蓝色
  punishment: 'bg-gray-500',    // 处罚 - 灰色
  meal: 'bg-pink-500'           // 报餐 - 粉色
}
```

## 跨列显示功能

### 🔄 跨列事件处理
支持连续多天的事项在日历网格中跨列显示：

#### 同一行跨列
```
┌─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│  1  │  2  │  3  │  4  │  5  │  6  │  7  │
│     │ ┌─────────────────────────┐ │     │
│     │ │      年假 (8-10日)      │ │     │
│     │ └─────────────────────────┘ │     │
└─────┴─────┴─────┴─────┴─────┴─────┴─────┘
```

#### 跨行显示
```
┌─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│  1  │  2  │  3  │  4  │  5  │  6  │  7  │
│     │     │     │ ┌─────────────────────┐
│     │     │     │ │  春节假期 (第1段)   │
│     │     │     │ └─────────────────────┘
├─────┼─────┼─────┼─────┼─────┼─────┼─────┤
│ ┌─────────────────────────────────────┐ │
│ │        春节假期 (第2段)              │ │
│ └─────────────────────────────────────┘ │
└─────┴─────┴─────┴─────┴─────┴─────┴─────┘
```

## 组件架构

### 🏗️ 核心组件

#### 1. CalendarHeader
- 事项类型切换按钮
- 月份导航
- 今天按钮

```tsx
<CalendarHeader
  activeEventType={activeEventType}
  onEventTypeChange={setActiveEventType}
  // ... 其他props
/>
```

#### 2. CalendarGrid
- 日历网格显示
- 单日事件渲染
- 跨列事件渲染

```tsx
<CalendarGrid
  events={getSingleDayEvents()}
  spanningEvents={getSpanningEvents()}
  // ... 其他props
/>
```

#### 3. SpanningEvent
- 跨列事件专用组件
- 自动计算跨列位置
- 支持跨行分段显示

```tsx
<SpanningEvent
  event={event}
  startCol={startCol}
  endCol={endCol}
  row={row}
/>
```

### 🔧 核心 Hooks

#### useEventsByType
```tsx
const {
  activeEventType,        // 当前激活的事项类型
  setActiveEventType,     // 切换事项类型
  activeEvents,           // 当前类型的所有事件
  getSingleDayEvents,     // 获取单日事件
  getSpanningEvents,      // 获取跨列事件
  getEventsForDate,       // 获取指定日期的事件
  addEvent,               // 添加事件
  removeEvent,            // 删除事件
  updateEvent,            // 更新事件
  getEventStats           // 获取事件统计
} = useEventsByType()
```

## 示例数据

### 📅 日历事项
```tsx
calendar: [
  {
    id: 'cal-1',
    title: '学习计划制定',
    startDate: 11,
    endDate: 11,
    startTime: '8:30',
    endTime: '17:30',
    location: '1#小时',
    organizer: '举办单位',
    eventType: 'calendar'
  }
]
```

### 🏥 请假事项
```tsx
leave: [
  {
    id: 'leave-1',
    title: '年假',
    startDate: 8,
    endDate: 10,
    eventType: 'leave',
    description: '年假休息'
  }
]
```

### 🎉 假期事项
```tsx
holiday: [
  {
    id: 'holiday-1',
    title: '春节假期',
    startDate: 1,
    endDate: 7,
    eventType: 'holiday',
    description: '法定节假日'
  }
]
```

## 交互功能

### 🖱️ 事项类型切换
- 点击顶部按钮切换不同事项类型
- 激活状态用对应颜色高亮显示
- 实时更新日历显示内容

### 📱 响应式设计
- 移动端：事项类型按钮自动换行
- 桌面端：水平排列所有按钮
- 跨列事件自适应容器宽度

### 🎯 事件交互
- 悬浮显示完整事件信息
- 点击事件可执行自定义操作
- 支持事件的增删改查

## 技术实现

### 🔄 跨列计算算法
```tsx
const calculateSpanningEvents = (events, days, currentMonth, currentYear) => {
  // 1. 找到事件在日历网格中的位置
  // 2. 计算开始和结束列
  // 3. 处理跨行情况
  // 4. 返回位置信息数组
}
```

### 🎨 CSS 定位
```tsx
style={{
  gridColumnStart: startCol,
  gridColumnEnd: endCol + 1,
  gridRow: row,
  left: `${((startCol - 1) / 7) * 100}%`,
  width: `${(spanCols / 7) * 100}%`,
  position: 'absolute',
  zIndex: 10
}}
```

## 使用示例

### 基本使用
```tsx
function MyCalendar() {
  const {
    activeEventType,
    setActiveEventType,
    getSingleDayEvents,
    getSpanningEvents
  } = useEventsByType()

  return (
    <CalendarGrid
      events={getSingleDayEvents()}
      spanningEvents={getSpanningEvents()}
      // ... 其他props
    />
  )
}
```

### 添加新事件
```tsx
const { addEvent } = useEventsByType()

const handleAddEvent = () => {
  addEvent('leave', {
    id: 'new-leave',
    title: '病假',
    startDate: 15,
    endDate: 16,
    eventType: 'leave',
    description: '感冒请假'
  })
}
```

## 扩展能力

### 🔧 自定义事项类型
```tsx
// 添加新的事项类型
const customEventTypes = [
  ...eventTypes,
  { key: 'meeting', label: '会议', color: 'bg-cyan-500' }
]
```

### 📊 数据持久化
```tsx
// 可扩展到服务器存储
const { eventsByType } = useEventsByType()
localStorage.setItem('calendarEvents', JSON.stringify(eventsByType))
```

### 🎨 主题定制
```tsx
// 自定义颜色主题
const customColors = {
  leave: 'bg-red-600',
  holiday: 'bg-purple-600'
}
```

## 性能优化

### ⚡ 渲染优化
- 使用 `useMemo` 缓存计算结果
- 条件渲染减少不必要的组件
- 虚拟化处理大量事件

### 🔄 状态管理
- 按需加载事件数据
- 智能的状态更新策略
- 避免不必要的重新渲染

## 总结

这个多事项类型日历系统提供了：

✅ **8种事项类型** - 覆盖工作生活的各个方面  
✅ **跨列显示** - 连续天数的事项可视化展示  
✅ **类型切换** - 快速切换查看不同类型的事项  
✅ **响应式设计** - 适配各种屏幕尺寸  
✅ **交互友好** - 丰富的用户交互功能  
✅ **扩展性强** - 易于添加新的事项类型和功能  

这个系统为用户提供了一个功能完整、使用便捷的日历管理工具！
