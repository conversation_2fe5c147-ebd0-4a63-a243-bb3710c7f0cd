import { FunctionComponent } from "react";
import {
    NavigationMenu,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Link } from "@radix-ui/react-navigation-menu";
import { ChevronLeft } from "lucide-react";
import { useRosterContext } from '../context/RosterContext';

interface RosterToolbarProps {
    activeTab: string;
    onTabChange: (tab: string) => void;
}

export const RosterToolbar: FunctionComponent<RosterToolbarProps> = (props) => {
    const { classifyList } = useRosterContext();
    return (
        <NavigationMenu>
            <NavigationMenuList className="p-2">
                <NavigationMenuItem className="ml-2" >
                    <Link href="/" >
                        <ChevronLeft />
                    </Link>
                </NavigationMenuItem>
                {/* 动态分类菜单 */}
                {classifyList && classifyList.map(classify => (
                    <NavigationMenuItem key={classify.classify}>
                        <NavigationMenuLink
                            className={
                                navigationMenuTriggerStyle() +
                                (props.activeTab === classify.classify
                                    ? " bg-accent text-accent-foreground"
                                    : "")
                            }
                            onClick={() => props.onTabChange(classify.classify)}
                        >
                            {classify.description}
                        </NavigationMenuLink>
                    </NavigationMenuItem>
                ))}
            </NavigationMenuList>
        </NavigationMenu>
    );
}; 