import http from '@/services/index'
import { CustomSuccessData } from '@/services/index'
import { TeamListItem } from '@/modules/roster/types/team'
import { ClassifyItem } from '@/modules/roster/types/classify'


// 获取组织架构
export const getTeamListApi = (data?: any): Promise<CustomSuccessData<TeamListItem[]>> => {
  return http.get<TeamListItem[]>("/api/file-user/team", data) as Promise<CustomSuccessData<TeamListItem[]>>;
};

// 数字人分类列表
export const gteClassifyList = (data?: any): Promise<CustomSuccessData<ClassifyItem[]>> => {
  return http.post<ClassifyItem[]>("/api/front/roster/classify_list", data);
};

interface EditInfoReqType {
  id?: string;
  user_id: number;
  values: Record<string, any>;
}

// 编辑用户信息
export const editUserInfo = (data: EditInfoReqType): Promise<CustomSuccessData<any>> => {
  return http.post<any>("/api/front/roster/edit_info", data);
};

interface UserDetailReqType {
  user_id: number;
  category: string;
}

// 数字人用户详情
export const getUserDetail = (data: UserDetailReqType): Promise<CustomSuccessData<any>> => {
  return http.post<any>("/api/front/roster/user_detail", data);
};


interface RemoveRecordReqType {
  id: number;
  user_id: number;
  category: string;
}

// 删除用户记录
export const removeRecord = (data: RemoveRecordReqType): Promise<CustomSuccessData<any>> => {
  return http.post<any>("/api/front/roster/remove_record", data);
};



// 获取角色统计模板列表
export const getTemplateList = (data?: any): Promise<CustomSuccessData<any>> => {
  return http.post<any>("/api/front/roster/template_list", data);
};

interface StatisticsReqType {
  user_id?: number; //用户ID
  team_id?: number;  //部门id
  template_id?: number; //模板id
}

// 查询统计模板列表详情
export const getStatistics = (data: StatisticsReqType): Promise<CustomSuccessData<any>> => {
  return http.post<any>("/api/front/roster/statistics", data);
};


// 查询所有角色
export const getListUserAll = (data: { name?: Array<number>; }) => {
  return http.post("/api/front/role/list_all", data);
};

// 数字人用户详情
export const getSelfInfo = (data: {category: string}) => {
  return http.post("/api/front/digital/self_info", data);
};