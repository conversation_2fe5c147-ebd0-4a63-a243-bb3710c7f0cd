import { useState, useRef } from 'react';
import { getListUserAll, getTeamListApi } from '@/api/roster';
// TODO: import user list api

export interface DynamicSelectorOption {
  label: string;
  value: number | string;
  [key: string]: any;
}

interface UseDynamicSelectorProps {
  type: 'Role' | 'Team' | 'User';
  multiple?: boolean;
  value: any;
  onChange: (val: any) => void;
}

export function useDynamicSelector({ type, multiple = false, value, onChange }: UseDynamicSelectorProps) {
  const [options, setOptions] = useState<DynamicSelectorOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectorVisible, setSelectorVisible] = useState(false);
  const loadedRef = useRef(false);

  // 选项加载
  const loadOptions = async () => {
    if (loadedRef.current) return;
    setLoading(true);
    setError(null);
    try {
      let opts: DynamicSelectorOption[] = [];
      if (type === 'Role') {
        const res = await getListUserAll({});
        let records: any[] = [];
        if (res && typeof res === 'object') {
          const data = (res as any).data;
          if (data && Array.isArray(data.records)) {
            records = data.records;
          } else if (Array.isArray((res as any).records)) {
            records = (res as any).records;
          }
        }
        opts = Array.isArray(records)
          ? records.map((item: any) => ({
              label: Array.isArray(item.name) ? item.name[0] : String(item.name),
              value: item.id,
              raw: item,
            }))
          : [];
      } else if (type === 'Team') {
        const res = await getTeamListApi();
        let teams: any[] = [];
        if (res && typeof res === 'object') {
          const data = (res as any).data || (res as any).d;
          if (Array.isArray(data)) teams = data;
        }
        opts = teams.map((item: any) => ({
          label: item.name,
          value: item.id,
          raw: item,
        }));
      } else if (type === 'User') {
        // TODO: 用户列表API
        opts = [];
      }
      setOptions(opts);
      loadedRef.current = true;
    } catch (e: any) {
      setError(e?.message || '选项加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 弹窗控制
  const openSelector = () => setSelectorVisible(true);
  const closeSelector = () => setSelectorVisible(false);

  // 回显label
  const getLabel = (val: any) => {
    if (multiple && Array.isArray(val)) {
      return val.map(v => {
        const opt = options.find(o => o.value === v);
        return opt ? opt.label : v;
      }).join(', ');
    } else {
      const opt = options.find(o => o.value === val);
      return opt ? opt.label : val;
    }
  };



  return {
    options,
    value,
    onChange,
    loading,
    error,
    openSelector,
    closeSelector,
    selectorVisible,
    getLabel,
    loadOptions,
  };
} 