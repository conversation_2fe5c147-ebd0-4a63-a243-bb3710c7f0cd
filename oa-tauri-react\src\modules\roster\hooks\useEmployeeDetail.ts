import { useState, useEffect, useCallback } from 'react';
import { getUserDetail } from '@/api/roster';

export function useEmployeeDetail(user_id: number , category: string) {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchDetail = useCallback(() => {
    if (!user_id || !category) return;
    setLoading(true);
    setError(null);
    getUserDetail({ user_id, category })
      .then(res => setData(res.data))
      .catch(err => setError(err as Error))
      .finally(() => setLoading(false));
  }, [user_id, category]);

  useEffect(() => {
    fetchDetail();
  }, [fetchDetail]);

  return { data, loading, error, refresh: fetchDetail };
} 