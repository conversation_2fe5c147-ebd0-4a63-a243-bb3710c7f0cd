import { <PERSON>, <PERSON><PERSON><PERSON>, Clock, Cake, FileText } from "lucide-react"
import { useTemplateList, TemplateItem } from '@/modules/template/hooks/useTemplateList'

interface AdminSidebarProps {
  selectedId?: number
  onSelect: (id: number) => void
}

export function AdminSidebar({ selectedId, onSelect }: AdminSidebarProps) {
  const { templates, loading, error } = useTemplateList();
  const icons = [Calendar, BarChart, Clock, Cake, FileText];

  return (
    <div className="w-64 border border-gray-200 bg-white">
      <div className="p-4">
        <div className="space-y-2">
          {loading ? (
            <div className="text-gray-400">加载模板列表中...</div>
          ) : error ? (
            <div className="text-red-500">{error}</div>
          ) : templates.length === 0 ? (
            <div className="text-gray-400">暂无模板</div>
          ) : (
            templates.map((item: TemplateItem, idx: number) => {
              const Icon = icons[idx % icons.length] || FileText;
              const isActive = selectedId === item.id;
              return (
                <button
                  key={item.id}
                  onClick={() => onSelect(item.id)}
                  className={`w-full flex items-center p-3 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <div className={`p-2 rounded-lg mr-3 ${
                    isActive ? 'bg-blue-100' : 'bg-gray-100'
                  }`}>
                    <Icon className={`h-6 w-6 ${
                      isActive ? 'text-blue-600' : 'text-gray-600'
                    }`} />
                  </div>
                  <div className="text-left">
                    <div className={`font-medium ${
                      isActive ? 'text-blue-700' : 'text-gray-900'
                    }`}>
                      {item.template_name}
                    </div>
                    <div className={`text-sm ${
                      isActive ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {item.template_description}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">统计范围: {item.team_ids?.join(', ') || '-'}</div>
                  </div>
                </button>
              )
            })
          )}
        </div>
      </div>
    </div>
  )
} 