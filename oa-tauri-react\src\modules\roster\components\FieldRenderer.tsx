import React, { useContext } from 'react';
import { TeamSelector } from '@/components/Selector/TeamSelector';
import { UserSelector } from '@/components/Selector/UserSelector';
import { Field } from './DynamicForm';
import { RoleSelector } from './RoleSelector';
import { useRoleList } from '../hooks/useRoleList';
import { TeamTreeContext, UserListContext } from './EmployeeDetailPanel';
import { TreeNode } from '@/utils/tree';
import { UserItem } from '../types/team';

interface FieldRendererProps {
  field: Field;
  value: any;
  isEditing: boolean;
  error?: string;
  onChange: (value: any) => void;
  teamSelector?: {
    visible: boolean;
    fieldId: string | null;
    setVisible: (visible: boolean, fieldId: string | null) => void;
  };
  teamTreeLoading?: boolean;
  teamTreeError?: any;
  userSelector?: {
    visible: boolean;
    fieldId: string | null;
    setVisible: (visible: boolean, fieldId: string | null) => void;
  };
}

const roleIdToName = (id: number, roles: { id: number; name: string }[]) => {
  const found = roles.find(r => r.id === id);
  return found ? found.name : id;
};

export const FieldRenderer: React.FC<FieldRendererProps> = ({
  field,
  value,
  isEditing,
  error,
  onChange,
  teamSelector,
  teamTreeLoading,
  teamTreeError,
  userSelector,
}) => {
  const required = field.is_required;
  const type = field.field_type;
  const baseInputClass = `block w-full rounded border border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/30 transition bg-white px-3 py-2 text-base leading-tight placeholder-gray-400 ${error ? 'border-red-400' : ''}`;
  const baseReadClass = 'block w-full rounded border border-gray-200 bg-gray-50 px-3 py-2 text-base text-gray-700 min-h-[40px]';

  let inputNode: React.ReactNode = null;

  const { roles } = useRoleList();
  const [roleSelectorVisible, setRoleSelectorVisible] = React.useState(false);

  const teamTreeData = useContext(TeamTreeContext);
  const { list: userList, loading: userListLoading, error: userListError } = useContext(UserListContext);

  const teamIdToName = (id: any): string => {
    if (typeof id !== 'string' && typeof id !== 'number') {
      return '';
    }
    const findNodeName = (nodes: TreeNode[]): string | undefined => {
      for (const node of nodes) {
        if (node.id === id) {
          return node.name;
        }
        if (node.children) {
          const foundName = findNodeName(node.children);
          if (foundName) {
            return foundName;
          }
        }
      }
      return undefined;
    };
    return findNodeName(teamTreeData || []) || String(id);
  };

  const userIdToName = (id: any): string => {
    if (typeof id !== 'string' && typeof id !== 'number') {
      return '';
    }
    const foundUser = (userList || []).find((user: UserItem) => user.id === id);
    return foundUser ? foundUser.name : String(id);
  };

  switch (type) {
    case 'SingleLineText':
      inputNode = isEditing ? (
        <input
          type="text"
          className={baseInputClass}
          value={value}
          maxLength={field.length}
          required={required}
          onChange={e => onChange(e.target.value)}
        />
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
      break;
    case 'MultilineText':
      inputNode = isEditing ? (
        <textarea
          className={`${baseInputClass} min-h-[60px]`}
          value={value}
          maxLength={field.length}
          required={required}
          onChange={e => onChange(e.target.value)}
        />
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
      break;
    case 'Date':
      inputNode = isEditing ? (
        <input
          type="date"
          className={baseInputClass}
          value={value}
          required={required}
          onChange={e => onChange(e.target.value)}
        />
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
      break;
    case 'DateTime':
      inputNode = isEditing ? (
        <input
          type="datetime-local"
          className={baseInputClass}
          value={value}
          required={required}
          onChange={e => onChange(e.target.value)}
        />
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
      break;
    case 'Number':
      inputNode = isEditing ? (
        <input
          type="number"
          className={baseInputClass}
          value={value}
          required={required}
          onChange={e => onChange(e.target.value)}
        />
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
      break;
    case 'CustomSelector':
      inputNode = isEditing ? (
        <select
          className={baseInputClass}
          value={value}
          required={required}
          onChange={e => onChange(e.target.value)}
        >
          <option value="">请选择</option>
          {field.options?.map((opt,optIndex) => (
            <option key={optIndex} value={optIndex}>{opt}</option>
          ))}
        </select>
      ) : (
        <div className={baseReadClass}>
          {field.options?.find(opt => opt === value) || <span className="text-gray-400">未填写</span>}
        </div>
      );
      break;
    case 'Sex':
      inputNode = isEditing ? (
        <select
          className={baseInputClass}
          value={value}
          required={required}
          onChange={e => onChange(e.target.value)}
        >
          <option value="">请选择</option>
          <option value="male">男</option>
          <option value="female">女</option>
        </select>
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
      break;
    case 'Team':
      const teamDisplayValues = Array.isArray(value)
        ? value
        : (value !== null && value !== undefined ? [value] : []);

      if (!isEditing) {
        inputNode = (
          <div className={baseReadClass}>
            {teamDisplayValues.map(teamIdToName).join(', ') || <span className="text-gray-400">未填写</span>}
          </div>
        );
      } else {
        inputNode = (
          <>
            <div
              className={baseInputClass + ' cursor-pointer select-none'}
              tabIndex={0}
              role="button"
              onClick={e => {
                e.stopPropagation();
                teamSelector?.setVisible(true, field.id);
              }}
            >
              {teamDisplayValues.map(teamIdToName).join(', ') || '请选择'}
            </div>
            <TeamSelector
              visible={!!teamSelector?.visible && teamSelector?.fieldId === field.id}
              value={Array.isArray(value) ? value : value ? [value] : []}
              onChange={val => onChange(val)}
              onClose={() => teamSelector?.setVisible(false, null)}
              multiple={!!field.allow_multiple}
              treeData={teamTreeData || []}
            />
            {teamTreeLoading && <div className="text-xs text-gray-400 mt-1">组织架构加载中...</div>}
            {teamTreeError && <div className="text-xs text-red-400 mt-1">组织架构加载失败：{teamTreeError.message}</div>}
          </>
        );
      }
      break;
    case 'User':
      const userDisplayValues = Array.isArray(value)
        ? value
        : (value !== null && value !== undefined ? [value] : []);

      if (!isEditing) {
        inputNode = (
          <div className={baseReadClass}>
            {userDisplayValues.map(userIdToName).join(', ') || <span className="text-gray-400">未填写</span>}
          </div>
        );
      } else {
        inputNode = (
          <>
            <div
              className={baseInputClass + ' cursor-pointer select-none'}
              tabIndex={0}
              role="button"
              onClick={e => {
                e.stopPropagation();
                userSelector?.setVisible(true, field.id);
              }}
            >
              {userDisplayValues.map(userIdToName).join(', ') || '请选择'}
            </div>
            <UserSelector
              visible={!!userSelector?.visible && userSelector?.fieldId === field.id}
              value={Array.isArray(value) ? value : value ? [value] : []}
              onChange={val => onChange(val)}
              onClose={() => userSelector?.setVisible(false, null)}
              multiple={!!field.allow_multiple}
              userList={userList || []}
              loading={userListLoading}
              error={userListError}
            />
          </>
        );
      }
      break;
    case 'Role':
      const roleDisplayValues = Array.isArray(value)
        ? value
        : (value !== null && value !== undefined ? [value] : []);

      if (!isEditing) {
        inputNode = (
          <div className={baseReadClass}>
            {roleDisplayValues.map((id: number) => roleIdToName(id, roles)).join(', ') || <span className="text-gray-400">未填写</span>}
          </div>
        );
      } else {
        inputNode = (
          <>
            <div
              className={baseInputClass + ' cursor-pointer select-none'}
              tabIndex={0}
              role="button"
              onClick={e => {
                e.stopPropagation();
                setRoleSelectorVisible(true);
              }}
            >
              {roleDisplayValues.map((id: number) => roleIdToName(id, roles)).join(', ') || '请选择'}
            </div>
            <RoleSelector
              visible={roleSelectorVisible}
              value={Array.isArray(value) ? value : value ? [value] : []}
              onChange={val => onChange(val)}
              onClose={() => setRoleSelectorVisible(false)}
              multiple={!!field.allow_multiple}
            />
          </>
        );
      }
      break;
    default:
      inputNode = isEditing ? (
        <input
          type="text"
          className={baseInputClass}
          value={value}
          required={required}
          onChange={e => onChange(e.target.value)}
        />
      ) : (
        <div className={baseReadClass}>{value || <span className="text-gray-400">未填写</span>}</div>
      );
  }

  return inputNode;
}; 