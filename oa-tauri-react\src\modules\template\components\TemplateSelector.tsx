import React, { useState } from 'react';
import { useTemplateList, TemplateItem } from '../hooks/useTemplateList';
import { useTemplateStatistics } from '../hooks/useTemplateStatistics';

interface TemplateSelectorProps {
  activeModule?: string;
}

// 统计模板选择与详情展示组件
export const TemplateSelector: React.FC<TemplateSelectorProps> = ({ activeModule }) => {
  // 获取模板列表
  const { templates, loading: templatesLoading, error: templatesError } = useTemplateList();
  // 当前选中模板id
  const [selectedId, setSelectedId] = useState<number | undefined>(undefined);
  // 获取模板详情
  const { statistics, loading: statisticsLoading, error: statisticsError } = useTemplateStatistics(selectedId);

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-4">{activeModule ? `统计模块：${activeModule}` : '统计模板'}</h2>
      {/* 模板选择 */}
      <div className="mb-4">
        {templatesLoading ? (
          <div>加载模板列表中...</div>
        ) : templatesError ? (
          <div className="text-red-500">{templatesError}</div>
        ) : (
          <select
            className="border rounded px-3 py-2"
            value={selectedId ?? ''}
            onChange={e => setSelectedId(e.target.value ? Number(e.target.value) : undefined)}
          >
            <option value="">请选择模板</option>
            {templates.map((tpl: TemplateItem) => (
              <option key={tpl.id} value={tpl.id}>
                {tpl.template_name}（{tpl.template_description}）
              </option>
            ))}
          </select>
        )}
      </div>
      {/* 模板详情 */}
      <div className="bg-white rounded shadow p-4 min-h-[120px]">
        {statisticsLoading ? (
          <div>加载模板详情中...</div>
        ) : statisticsError ? (
          <div className="text-red-500">{statisticsError}</div>
        ) : statistics ? (
          <pre className="text-xs text-gray-700 whitespace-pre-wrap">{JSON.stringify(statistics, null, 2)}</pre>
        ) : (
          <div className="text-gray-400">请选择模板以查看详情</div>
        )}
      </div>
    </div>
  );
}; 