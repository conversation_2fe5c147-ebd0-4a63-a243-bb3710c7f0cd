import React from 'react'
import { cn } from '@/lib/utils'
import { TopModule } from '../hooks/useTopModules'

interface TopModulesProps {
  modules: TopModule[]
  onModuleClick: (module: TopModule) => void
  className?: string
}

interface ModuleCardProps {
  module: TopModule
  onClick: (module: TopModule) => void
}

const ModuleCard: React.FC<ModuleCardProps> = ({ module, onClick }) => {
  return (
    <div
      className={cn(
        "group relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer",
        "border border-gray-100 hover:border-gray-200",
        "transform hover:-translate-y-1 hover:scale-105"
      )}
      onClick={() => onClick(module)}
    >
      <div className="p-6 flex flex-col items-center gap-3">
        {/* 图标容器 */}
        <div className={cn(
          "w-16 h-16 rounded-full flex items-center justify-center text-2xl",
          "bg-gradient-to-br from-gray-50 to-gray-100",
          "group-hover:from-blue-50 group-hover:to-blue-100",
          "transition-all duration-300"
        )}>
          <span className="transform group-hover:scale-110 transition-transform duration-300">
            {module.icon}
          </span>
        </div>
        
        {/* 标签 */}
        <div className="text-center">
          <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
            {module.label}
          </h3>
          {module.description && (
            <p className="text-xs text-gray-500 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              {module.description}
            </p>
          )}
        </div>
        
        {/* 悬浮效果装饰 */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 to-purple-500/0 group-hover:from-blue-500/5 group-hover:to-purple-500/5 transition-all duration-300" />
      </div>
    </div>
  )
}

export const TopModules: React.FC<TopModulesProps> = ({ 
  modules, 
  onModuleClick, 
  className 
}) => {
  return (
    <div className={cn("mb-8", className)}>
      {/* 标题 */}
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2">功能模块</h2>
        <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
      </div>
      
      {/* 模块网格 */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-9 gap-4">
        {modules.map((module) => (
          <ModuleCard
            key={module.id}
            module={module}
            onClick={onModuleClick}
          />
        ))}
      </div>
    </div>
  )
}
