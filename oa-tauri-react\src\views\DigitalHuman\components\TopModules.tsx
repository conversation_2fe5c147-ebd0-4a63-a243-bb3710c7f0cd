import React from 'react'
import { cn } from '@/lib/utils'
import { TopModule } from '../hooks/useTopModules'

interface TopModulesProps {
  modules: TopModule[]
  onModuleClick: (module: TopModule) => void
  className?: string
}

interface ModuleCardProps {
  module: TopModule
  onClick: (module: TopModule) => void
}

const ModuleCard: React.FC<ModuleCardProps> = ({ module, onClick }) => {
  return (
    <div
      className={cn(
        "bg-white p-4 rounded shadow cursor-pointer hover:shadow-md transition-shadow flex items-center gap-2 w-full",
        "transform hover:-translate-y-1 hover:scale-105 transition-all duration-300"
      )}
      onClick={() => onClick(module)}
    >
      {/* 图标容器 - 与 MenuItem 完全一致的样式 */}
      <div className="bg-[#f7b51c] text-xl rounded">{module.icon}</div>

      {/* 标签 - 与 MenuItem 保持一致 */}
      <span className="text-sm flex-1">{module.label}</span>
    </div>
  )
}

export const TopModules: React.FC<TopModulesProps> = ({ 
  modules, 
  onModuleClick, 
  className 
}) => {
  return (
    <div className={cn("mb-8", className)}>
      {/* 标题 */}
      <div className="mb-6">
        {/* <h2 className="text-xl font-bold text-gray-900 mb-2">功能模块</h2> */}
        {/* <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div> */}
      </div>

      {/* 模块网格 - 与 ManagementMenu 保持一致的布局 */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {modules.map((module) => (
          <ModuleCard
            key={module.id}
            module={module}
            onClick={onModuleClick}
          />
        ))}
      </div>
    </div>
  )
}
