import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { PencilIcon, SaveIcon } from "lucide-react"
import { Employee } from '../Roster/component/type'
import { useClassifyList } from '@/hooks/useClassifyList'

interface childProps {
    rosterData: Employee
}

export default function Child({ rosterData }: childProps) {
    const [data, setData] = useState<any>(null)
    const [isEditing, setIsEditing] = useState(false)
    const [activeTab, setActiveTab] = useState("")
    const { data: classifyList } = useClassifyList()

    useEffect(() => {
        console.log('切换 ID:', rosterData);

        setData(rosterData)
    }, [rosterData])

    useEffect(() => {
        // 默认选中第一个tab
        if (classifyList && classifyList.length > 0 && classifyList[0].sub_items.length > 0) {
            setActiveTab(classifyList[0].sub_items[0].category)
        }
    }, [classifyList])

    const handleEdit = () => {
        setIsEditing(!isEditing)
    }

    const handleSave = () => {
        setIsEditing(false)
        // 这里可以添加保存逻辑
        // emit('save-data', data)
    }

    return (
        <div className="bg-gray-50 min-h-screen">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">员工档案 -  {data?.name}</h2>
                <Button
                    variant={isEditing ? "default" : "outline"}
                    size="sm"
                    onClick={isEditing ? handleSave : handleEdit}
                >
                    {isEditing ? (
                        <>
                            <SaveIcon className="h-4 w-4 mr-2" />
                            保存
                        </>
                    ) : (
                        <>
                            <PencilIcon className="h-4 w-4 mr-2" />
                            编辑
                        </>
                    )}
                </Button>
            </div>

            {data ? (
                <>
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
                        <TabsList className={`grid grid-cols-${classifyList[0].sub_items.length} mb-4`}>
                            {classifyList[0].sub_items.map(sub => (
                                <TabsTrigger value={sub.category} key={sub.category} onClick={() => setActiveTab(sub.category)}>
                                    {sub.description}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                        {classifyList[0].sub_items.map(sub => (
                            <TabsContent value={sub.category} key={sub.category} className="bg-white rounded-lg shadow-sm p-6">
                                {/* 这里可根据sub.category动态渲染内容，暂用占位 */}
                                <div className="text-gray-700">{sub.description}内容区（待实现）</div>
                            </TabsContent>
                        ))}
                    </Tabs>

                    <div className="bg-white rounded-lg shadow-sm border-t px-6 py-3 text-xs text-gray-500 flex justify-between">
                        <span>最后更新时间: {data.lastUpdated || '2023年5月6日 16:32分'}</span>
                        <span>更新记录</span>
                    </div>
                </>
            ) : (
                <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500">
                    正在加载员工信息...
                </div>
            )}
        </div>
    )
}

// 兼容懒加载
export const Component = Child
