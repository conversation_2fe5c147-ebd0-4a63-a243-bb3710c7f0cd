# 样式一致性说明

## 设计目标

确保数字人日历首页的顶部功能模块与首页管理菜单保持完全一致的视觉样式，同时保留优秀的交互动效。

## 样式对比

### ManagementMenu 原始样式
```tsx
// management-menu.tsx - MenuItem 组件
<div className="bg-white p-4 rounded shadow cursor-pointer hover:shadow-md transition-shadow flex items-center gap-2 w-full">
  <div className="bg-[#f7b51c] text-xl rounded">{icon}</div>
  <span className="text-sm flex-1">{label}</span>
</div>
```

### TopModules 统一后样式
```tsx
// TopModules.tsx - ModuleCard 组件
<div className="bg-white p-4 rounded shadow cursor-pointer hover:shadow-md transition-shadow flex items-center gap-2 w-full transform hover:-translate-y-1 hover:scale-105 transition-all duration-300">
  <div className="bg-[#f7b51c] text-xl rounded">{icon}</div>
  <span className="text-sm flex-1">{label}</span>
</div>
```

## 保持一致的元素

### 1. 容器样式
- ✅ `bg-white` - 白色背景
- ✅ `p-4` - 内边距
- ✅ `rounded` - 圆角
- ✅ `shadow` - 阴影
- ✅ `cursor-pointer` - 鼠标指针
- ✅ `hover:shadow-md` - 悬浮阴影
- ✅ `transition-shadow` - 阴影过渡
- ✅ `flex items-center gap-2` - 弹性布局
- ✅ `w-full` - 全宽

### 2. 图标容器
- ✅ `bg-[#f7b51c]` - 橙黄色背景
- ✅ `text-xl` - 文字大小
- ✅ `rounded` - 圆角

### 3. 文字标签
- ✅ `text-sm` - 文字大小
- ✅ `flex-1` - 弹性占满剩余空间

### 4. 网格布局
- ✅ `grid-cols-2 md:grid-cols-4 lg:grid-cols-7` - 响应式网格
- ✅ `gap-4` - 网格间距

## 增强的交互效果

在保持基础样式一致的前提下，我们为 TopModules 添加了额外的交互动效：

### 悬浮动画
```css
transform hover:-translate-y-1 hover:scale-105 transition-all duration-300
```

**效果说明：**
- `hover:-translate-y-1` - 悬浮时向上移动 4px
- `hover:scale-105` - 悬浮时放大 5%
- `transition-all duration-300` - 300ms 的平滑过渡

### 动画时机
- **进入**: 鼠标悬浮时触发提升和缩放
- **退出**: 鼠标离开时平滑回到原位
- **点击**: 保持原有的阴影变化效果

## 视觉一致性验证

### 颜色一致性
- ✅ 背景色: `#ffffff` (bg-white)
- ✅ 图标背景: `#f7b51c` (bg-[#f7b51c])
- ✅ 文字颜色: 默认黑色
- ✅ 阴影颜色: 默认灰色阴影

### 尺寸一致性
- ✅ 内边距: `16px` (p-4)
- ✅ 圆角: `6px` (rounded)
- ✅ 间距: `8px` (gap-2)
- ✅ 文字大小: `14px` (text-sm)

### 布局一致性
- ✅ 弹性布局: `flex items-center`
- ✅ 图标位置: 左侧固定
- ✅ 文字位置: 右侧自适应
- ✅ 网格响应: 2/4/7 列布局

## 用户体验提升

### 1. 视觉连贯性
用户在不同页面看到相同的设计语言，提升品牌一致性和用户认知。

### 2. 交互反馈
增强的悬浮效果提供更好的交互反馈，让用户清楚知道哪些元素可以点击。

### 3. 现代化体验
平滑的动画过渡符合现代 Web 应用的交互标准。

## 技术实现

### CSS 类组合
```tsx
className={cn(
  // 基础样式 (与 MenuItem 一致)
  "bg-white p-4 rounded shadow cursor-pointer hover:shadow-md transition-shadow flex items-center gap-2 w-full",
  // 增强动效
  "transform hover:-translate-y-1 hover:scale-105 transition-all duration-300"
)}
```

### 性能考虑
- 使用 CSS transform 而非改变布局属性，确保动画性能
- 合理的过渡时间 (300ms) 平衡流畅度和响应性
- 避免过度动画，保持专业感

## 维护指南

### 样式同步
当 `management-menu.tsx` 的样式发生变化时，需要同步更新 `TopModules.tsx` 的基础样式部分。

### 动效调整
如需调整动画效果，只需修改 transform 和 transition 相关的类名，不影响基础样式。

### 扩展性
新增功能模块时，自动继承统一的样式和动效，无需额外配置。
