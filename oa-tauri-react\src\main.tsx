import React from 'react'
import ReactDOM from 'react-dom/client'
import App from '@/app'

(async () => {
    let initialPath = '/';

    // 从 URL 中获取 token 参数
    const params = new URLSearchParams(window.location.search);
    const urlToken = params.get('token');
    const url = params.get('uri') || "";

    console.log(url, urlToken,111111111111111);
    // 存储 token
    if (urlToken) {
        localStorage.setItem('token', urlToken);
        localStorage.setItem('BaseUrl', url);
    }

    // 页面跳转（考虑 hash 模式时的兼容）
    const currentUrl = window.location.pathname + window.location.search + window.location.hash;

    if (!currentUrl.includes(initialPath)) {
        // 用 replace 避免历史记录堆积
        window.location.replace(initialPath);
        return;
    }

    ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
        <React.StrictMode>
            <App />
        </React.StrictMode>
    )
})();
