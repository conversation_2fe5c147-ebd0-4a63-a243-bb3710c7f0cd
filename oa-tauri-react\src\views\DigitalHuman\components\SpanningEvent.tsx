import React from 'react'
import { cn } from '@/lib/utils'
import { CalendarEventData } from './CalendarEvent'

interface SpanningEventProps {
  event: CalendarEventData
  startCol: number // 开始列 (1-7)
  endCol: number   // 结束列 (1-7)
  row: number      // 所在行
  className?: string
}

const eventColors = {
  calendar: 'bg-blue-500',
  attendance: 'bg-green-500',
  leave: 'bg-red-500',
  holiday: 'bg-purple-500',
  overtime: 'bg-orange-500',
  task: 'bg-indigo-500',
  punishment: 'bg-gray-500',
  meal: 'bg-pink-500'
}

export const SpanningEvent: React.FC<SpanningEventProps> = ({
  event,
  startCol,
  endCol,
  row,
  className
}) => {
  const eventColor = event.color || eventColors[event.eventType]
  const spanCols = endCol - startCol + 1
  
  // 计算网格位置
  const gridColumnStart = startCol
  const gridColumnEnd = endCol + 1
  
  return (
    <div
      className={cn(
        "absolute z-10 text-xs text-white px-2 py-1 rounded font-medium shadow-sm cursor-pointer transition-all duration-200 hover:shadow-md",
        eventColor,
        className
      )}
      style={{
        gridColumnStart,
        gridColumnEnd,
        gridRow: row,
        left: `${((startCol - 1) / 7) * 100}%`,
        width: `${(spanCols / 7) * 100}%`,
        top: '2px',
        height: 'calc(100% - 4px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      title={`${event.title} (${event.startDate}-${event.endDate}日)`}
    >
      <span className="truncate text-center">
        {event.title} ({event.startDate}-{event.endDate}日)
      </span>
    </div>
  )
}

// 计算事件的跨列信息
export const calculateSpanningEvents = (
  events: CalendarEventData[],
  days: any[],
  currentMonth: number,
  currentYear: number
) => {
  const spanningEvents: Array<{
    event: CalendarEventData
    startCol: number
    endCol: number
    row: number
  }> = []

  events.forEach((event) => {
    // 找到开始和结束日期在日历网格中的位置
    const startDayIndex = days.findIndex(day => 
      day.date === event.startDate && day.isCurrentMonth
    )
    const endDayIndex = days.findIndex(day => 
      day.date === event.endDate && day.isCurrentMonth
    )

    if (startDayIndex !== -1 && endDayIndex !== -1) {
      const startCol = (startDayIndex % 7) + 1
      const endCol = (endDayIndex % 7) + 1
      const startRow = Math.floor(startDayIndex / 7) + 1
      const endRow = Math.floor(endDayIndex / 7) + 1

      // 如果事件跨越多行，需要分段处理
      if (startRow === endRow) {
        // 同一行
        spanningEvents.push({
          event,
          startCol,
          endCol,
          row: startRow
        })
      } else {
        // 跨行处理
        // 第一行：从开始列到第7列
        spanningEvents.push({
          event: { ...event, title: `${event.title} (第1段)` },
          startCol,
          endCol: 7,
          row: startRow
        })

        // 中间的完整行
        for (let row = startRow + 1; row < endRow; row++) {
          spanningEvents.push({
            event: { ...event, title: `${event.title} (第${row - startRow + 1}段)` },
            startCol: 1,
            endCol: 7,
            row
          })
        }

        // 最后一行：从第1列到结束列
        if (endRow > startRow) {
          spanningEvents.push({
            event: { ...event, title: `${event.title} (最后段)` },
            startCol: 1,
            endCol,
            row: endRow
          })
        }
      }
    }
  })

  return spanningEvents
}
