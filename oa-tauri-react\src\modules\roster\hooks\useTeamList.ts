import { useEffect, useState, useCallback } from 'react'
import { getTeamListApi } from '@/api/roster'
import { teamListToTree } from '@/utils/tree'
import { TeamListItem } from '@/modules/roster/types/team'

interface TreeNode extends TeamListItem {
  children?: TreeNode[];
}

export function useRosterTeamList() {
  const [data, setData] = useState<TreeNode[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = useCallback(() => {
    setLoading(true)
    setError(null)
    getTeamListApi()
      .then((res) => {
        const list: TeamListItem[] = res.data || res.d || []
        setData(teamListToTree(list))
      })
      .catch(err => {
        setError(err as Error)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refresh: fetchData }
} 