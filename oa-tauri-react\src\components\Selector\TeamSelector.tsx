import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { UserCircle, ChevronRight, ChevronDown } from 'lucide-react';
import { MdFolder } from 'react-icons/md';

interface TeamNode {
  id: number | string;
  name: string;
  type: string;
  pid?: number | string;
  children?: TeamNode[];
}

interface TeamSelectorProps {
  visible: boolean;
  value: (number | string)[];
  onChange: (val: (number | string)[]) => void;
  onClose: () => void;
  multiple?: boolean;
  treeData: TeamNode[];
}

export const TeamSelector: React.FC<TeamSelectorProps> = ({ visible, value, onChange, onClose, multiple = true, treeData = [] }) => {
  const [selectedKeys, setSelectedKeys] = useState<(number | string)[]>(value || []);
  const [expanded, setExpanded] = useState<{ [key: string]: boolean }>({});
  const [search, setSearch] = useState('');

  useEffect(() => {
    setSelectedKeys(value || []);
    setExpanded({});
  }, [value, visible]);

  const toggleExpand = (id: string | number) => {
    setExpanded(prev => ({ ...prev, [id]: !prev[id] }));
  };

  // 搜索过滤树
  const filterTree = (node: TeamNode, lower: string): TeamNode | null => {
    if (!node) return null;
    if (node.type === 'user') {
      if (node.name.toLowerCase().includes(lower)) {
        return node;
      }
      return null;
    }
    const filteredChildren = (node.children || [])
      .map((child: any) => filterTree(child, lower))
      .filter(Boolean);
    if (node.name.toLowerCase().includes(lower) || filteredChildren.length > 0) {
      return { ...node, children: filteredChildren as TeamNode[] };
    }
    return null;
  };

  const renderTreeNode = (node: TeamNode, depth = 0) => {
    if (!node) return null;
    if (node.type === 'user') {
      return (
        <li
          key={node.id}
          className={`flex items-center px-2 py-1 rounded text-sm ${selectedKeys.includes(node.id) ? 'bg-blue-100 text-blue-600' : 'text-gray-700'}`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
        >
          <UserCircle className="w-6 h-6 text-gray-400 mr-2 scale-80" />
          <span className="flex-1">{node.name}</span>
        </li>
      );
    }
    const isExpanded = expanded[node.id] || !!search;
    return (
      <div key={node.id} className="mb-1">
        <div
          className={`flex items-center px-2 py-1 rounded cursor-pointer ${isExpanded ? 'bg-gray-100' : 'hover:bg-gray-50'}`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => toggleExpand(node.id)}
        >
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-gray-400 mr-1" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-400 mr-1" />
          )}
          <input
            type="checkbox"
            className="mr-2 w-4 h-4 text-primary rounded border-gray-300 focus:ring-primary"
            checked={selectedKeys.includes(node.id)}
            onChange={e => {
              e.stopPropagation(); // Prevent toggling expand when clicking checkbox
              if (multiple) {
                setSelectedKeys(keys =>
                  e.target.checked ? [...keys, node.id] : keys.filter(k => k !== node.id)
                );
              } else {
                setSelectedKeys([node.id]);
                onChange([node.id]);
                onClose();
              }
            }}
          />
          <MdFolder className="mr-2 w-5 h-5 text-[#7da4d1]" />
          <span className="font-medium text-sm flex-1 truncate">{node.name}</span>
        </div>
        {isExpanded && node.children && node.children.length > 0 && (
          <ul className="mt-1">
            {node.children.map(child => renderTreeNode(child, depth + 1))}
          </ul>
        )}
      </div>
    );
  };

  // 过滤顶层
  const filteredTree = search
    ? (treeData.map(node => filterTree(node, search.toLowerCase())).filter(Boolean) as TeamNode[])
    : treeData;

  const handleOk = () => {
    onChange(selectedKeys);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>选择组织/用户</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <input
            placeholder="搜索名称"
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="mb-3 border rounded px-2 py-1"
          />
          <div className="max-h-[300px] overflow-y-auto border rounded-md p-2">
            {filteredTree.length === 0 ? (
              <div className="text-center text-gray-500">无匹配数据</div>
            ) : (
              filteredTree.map(node => renderTreeNode(node))
            )}
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={handleCancel}>取消</Button>
          </DialogClose>
          {multiple && <Button type="submit" onClick={handleOk}>确认</Button>}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 