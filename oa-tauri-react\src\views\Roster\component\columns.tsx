import * as React from "react"
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table"
import { MoreHorizontal, Sheet, ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react"


import { cn } from "@/lib/utils"
import { DataTableColumnHeaderProps, DataTableViewOptionsProps, Employee } from "./type"

import { Button } from "@/components/ui/button"
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"

import { openWindow } from "@/utils"
import { emit } from "@tauri-apps/api/event"



export function DataTableViewOptions<TData>({
    table,
}: DataTableViewOptionsProps<TData>) {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    size="sm"
                    className="ml-auto hidden h-8 lg:flex"
                >
                    <Sheet />
                    自定义列
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[150px]">
                <DropdownMenuLabel>展示列</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {table
                    .getAllColumns()
                    .filter(
                        (column: any) =>
                            typeof column.accessorFn !== "undefined" && column.getCanHide()
                    )
                    .map((column: any) => {
                        return (
                            <DropdownMenuCheckboxItem
                                key={column.id}
                                className="capitalize"
                                checked={column.getIsVisible()}
                                onCheckedChange={(value) => column.toggleVisibility(!!value)}
                            >
                                {column.columnDef.meta?.title ?? column.id}
                            </DropdownMenuCheckboxItem>
                        )
                    })}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}



export function DataTableColumnHeader<TData, TValue>({
    column,
    title,
    className,
}: DataTableColumnHeaderProps<TData, TValue>) {
    if (!column.getCanSort()) {
        return <div className={cn(className)}>{title}</div>
    }

    return (
        <div className={cn("flex items-center space-x-2", className)}>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        size="sm"
                        className="-ml-3 h-8 data-[state=open]:bg-accent"
                    >
                        <span>{title}</span>
                        {column.getIsSorted() === "desc" ? (
                            <ArrowDown />
                        ) : column.getIsSorted() === "asc" ? (
                            <ArrowUp />
                        ) : (
                            <ChevronsUpDown />
                        )}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                    <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
                        <ArrowUp className="h-3.5 w-3.5 text-muted-foreground/70" />
                        升序
                    </DropdownMenuItem>

                    {/* <DropdownMenuSeparator /> */}
                    <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
                        <ArrowDown className="h-3.5 w-3.5 text-muted-foreground/70" />
                        降序
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    )
}



export const columns: ColumnDef<Employee>[] = [
    {
        id: "select",
        header: "序号",
        cell: ({ row }) => (
            <div>{row.index + 1}</div>
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "name",
        header: "姓名",
        meta: { title: "姓名" },
    },
    {
        accessorKey: "jobNumber",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="工号/工牌" />
        ),
        meta: { title: "工号/工牌" },
    },
    {
        accessorKey: "gender",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="性别" />
        ),
        meta: { title: "性别" },
    },
    {
        accessorKey: "age",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="年龄" />
        ),
        meta: { title: "年龄" },
    },
    {
        accessorKey: "status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="状态" />
        ),
        meta: { title: "状态" },
    },
    {
        accessorKey: "department",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="所属部门" />
        ),
        meta: { title: "所属部门" },
    },
    {
        accessorKey: "position",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="工作岗位" />
        ),
        meta: { title: "工作岗位" },
    },
    {
        accessorKey: "entryDate",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="入职日期" />
        ),
        meta: { title: "入职日期" },
    },
    {
        accessorKey: "contractDate",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="合同日期" />
        ),
        meta: { title: "合同日期" },
    },
    {
        accessorKey: "idCard",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="身份证" />
        ),
        meta: { title: "身份证" },
    },
    {
        accessorKey: "phone",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="电话" />
        ),
        meta: { title: "电话" },
    },
    {
        accessorKey: "address",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="地址" />
        ),
        meta: { title: "地址" },
    },
    {
        accessorKey: "education",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="学历" />
        ),
        meta: { title: "学历" },
    },
    {
        accessorKey: "major",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="专业" />
        ),
        meta: { title: "专业" },
    },
    {
        accessorKey: "bank",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="银行卡号" />
        ),
        meta: { title: "银行卡号" },
    },
    {
        accessorKey: "salary",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="工资" />
        ),
        meta: { title: "工资" },
    },
    {
        accessorKey: "socialSecurity",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="社保" />
        ),
        meta: { title: "社保" },
    },
    {
        accessorKey: "accumulationFund",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="公积金" />
        ),
        meta: { title: "公积金" },
    },
    {
        accessorKey: "taxNumber",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="税号" />
        ),
        meta: { title: "税号" },
    },
    {
        accessorKey: "emergencyContact",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="紧急联系人" />
        ),
        meta: { title: "紧急联系人" },
    },
    {
        accessorKey: "remarks",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="备注" />
        ),
        meta: { title: "备注" },
    },
    {
        id: "actions",
        enableHiding: false,
        cell: ({ row }) => {
            const employee = row.original
            // const { open } = useOpenWindow(`detail-${employee.index}`, '详细信息', {
            //     url: `/detail?id=${employee.index}`,
            //     width: 800,
            //     height: 600
            // });
            const openChild = async (item: any) => {
                // 创建更完整的员工数据
                const employeeData = {
                    ...item,
                    birthDate: '1990-05-15',
                    lunarBirthday: '1990年四月廿一',
                    gender: '男',
                    age: '33',
                    ethnicity: '汉族',
                    nativePlace: '江苏南京',
                    contactMethod: '手机',
                    email: '<EMAIL>',
                    idNumber: '320123199005150123',
                    maritalStatus: '已婚',
                    politicalStatus: '群众',
                    education: '本科',
                    degree: '学士',
                    languageSkills: '英语六级',
                    currentAddress: '上海市浦东新区张江高科技园区',
                    healthStatus: '良好',
                    height: '178cm',
                    weight: '70kg',
                    backgroundCheck: '已完成',
                    academicDegree: '计算机科学',
                    emergencyContact: '张三',
                    emergencyPhone: '13800138000',
                    emergencyRelation: '配偶',
                    notes: '2022年度优秀员工',
                    lastUpdated: '2023年5月6日 16:32分'
                };
                
                // 第一次点击才创建窗口
                openWindow({
                    label: `child-window${item.index}`,
                    url: `/child/${item.index}`,
                    width: 900,
                    height: 700,
                    initData: employeeData, // 初始化传入完整员工数据
                    onCreated: () => {
                        emit('init-data', employeeData) // 通知子窗口立即使用
                    },
                })
            }

            const updateChild = (item: any) => {
                // 创建更完整的员工数据
                const employeeData = {
                    ...item,
                    birthDate: '1990-05-15',
                    lunarBirthday: '1990年四月廿一',
                    gender: '男',
                    age: '33',
                    ethnicity: '汉族',
                    nativePlace: '江苏南京',
                    contactMethod: '手机',
                    email: '<EMAIL>',
                    idNumber: '320123199005150123',
                    maritalStatus: '已婚',
                    politicalStatus: '群众',
                    education: '本科',
                    degree: '学士',
                    languageSkills: '英语六级',
                    currentAddress: '上海市浦东新区张江高科技园区',
                    healthStatus: '良好',
                    height: '178cm',
                    weight: '70kg',
                    backgroundCheck: '已完成',
                    academicDegree: '计算机科学',
                    emergencyContact: '张三',
                    emergencyPhone: '13800138000',
                    emergencyRelation: '配偶',
                    notes: '2022年度优秀员工',
                    lastUpdated: '2023年5月6日 16:32分'
                };
                emit('update-data', employeeData) // ✅ 再次点击更新子窗口数据
            }

            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem
                            onClick={() => navigator.clipboard.writeText(employee.index.toString())}
                        >
                            复制ID
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => {
                            openChild(employee)
                            updateChild(employee)
                        }}>查看</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            )
        },
    },
]

const data: Employee[] = [
    {
        index: 1,
        name: "张三",
        jobNumber: "10023",
        gender: "男",
        age: "30",
        status: "在职",
        department: "广州总部/部门1",
        position: "前端开发",
        idCard: "440***********1234",
        phone: "138****5678",
        salary: "¥8000",
        departmentId: "dept1"
    },
    {
        index: 2,
        name: "李四",
        jobNumber: "10024",
        gender: "男",
        age: "28",
        status: "在职",
        department: "广州总部/部门2",
        position: "前端开发",
        idCard: "440***********5678",
        phone: "139****1234",
        salary: "¥8000",
        departmentId: "dept2"
    },
    {
        index: 3,
        name: "王五",
        jobNumber: "10025",
        gender: "男",
        age: "32",
        status: "在职",
        department: "广州总部/部门3",
        position: "前端开发",
        idCard: "440***********9012",
        phone: "137****5678",
        salary: "¥8000",
        departmentId: "dept3"
    }
]




export function DataTableDemo() {
    const [sorting, setSorting] = React.useState<SortingState>([])
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
        []
    )
    const [columnVisibility, setColumnVisibility] =
        React.useState<VisibilityState>({})
    const [rowSelection, setRowSelection] = React.useState({})

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    })

    return (
        <div className="w-full">
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    )
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <div className="flex items-center justify-end space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                    {table.getFilteredSelectedRowModel().rows.length} of{" "}
                    {table.getFilteredRowModel().rows.length} row(s) selected.
                </div>
                <div className="space-x-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => table.previousPage()}
                        disabled={!table.getCanPreviousPage()}
                    >
                        上一页
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => table.nextPage()}
                        disabled={!table.getCanNextPage()}
                    >
                        下一页
                    </Button>
                </div>
            </div>
        </div>
    )
}
