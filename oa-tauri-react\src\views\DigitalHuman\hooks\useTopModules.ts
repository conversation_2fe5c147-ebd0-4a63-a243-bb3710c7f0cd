import { useNavigate } from 'react-router'
import { useCallback } from 'react'

export interface TopModule {
  id: string
  icon: string
  label: string
  path?: string
  color?: string
  description?: string
}

const defaultModules: TopModule[] = [
  { 
    id: 'personal-info', 
    icon: "👤", 
    label: "私人信息", 
    path: "/digital-human/info",
    color: "bg-blue-500",
    description: "查看个人基本信息"
  },
  { 
    id: 'workspace', 
    icon: "💼", 
    label: "工作台", 
    path: "/workspace",
    color: "bg-green-500",
    description: "个人工作台"
  },
  { 
    id: 'meeting-room', 
    icon: "🏢", 
    label: "会议室", 
    path: "/meeting-room",
    color: "bg-purple-500",
    description: "会议室预订"
  },
  { 
    id: 'network-disk', 
    icon: "💾", 
    label: "网络硬盘", 
    path: "/network-disk",
    color: "bg-orange-500",
    description: "文件存储"
  },
  { 
    id: 'source-code', 
    icon: "🔧", 
    label: "国产源码", 
    path: "/source-code",
    color: "bg-red-500",
    description: "源码管理"
  },
  { 
    id: 'digital-human', 
    icon: "🤖", 
    label: "数字人", 
    path: "/digital-human/info",
    color: "bg-indigo-500",
    description: "数字人信息"
  },
  { 
    id: 'health-info', 
    icon: "🏥", 
    label: "健康信息", 
    path: "/health-info",
    color: "bg-pink-500",
    description: "健康档案"
  },
  { 
    id: 'employee-calendar', 
    icon: "📅", 
    label: "员工日历", 
    path: "/employee-calendar",
    color: "bg-teal-500",
    description: "日程安排"
  },
  { 
    id: 'member-info', 
    icon: "👥", 
    label: "成员信息", 
    path: "/member-info",
    color: "bg-cyan-500",
    description: "团队成员"
  },
]

export const useTopModules = (customModules?: TopModule[]) => {
  const navigate = useNavigate()
  const modules = customModules || defaultModules
  
  const handleModuleClick = useCallback((module: TopModule) => {
    if (module.path) {
      navigate(module.path)
    } else {
      console.log(`Module ${module.label} clicked, but no path defined`)
    }
  }, [navigate])
  
  const getModuleById = useCallback((id: string) => {
    return modules.find(module => module.id === id)
  }, [modules])
  
  const getModulesByCategory = useCallback((category: string) => {
    // 可以根据需要扩展分类功能
    return modules
  }, [modules])
  
  return {
    modules,
    handleModuleClick,
    getModuleById,
    getModulesByCategory
  }
}
