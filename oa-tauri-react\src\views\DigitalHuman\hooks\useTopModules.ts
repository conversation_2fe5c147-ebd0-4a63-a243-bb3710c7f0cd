import { useNavigate } from 'react-router'
import { useCallback } from 'react'

export interface TopModule {
  id: string
  icon: string
  label: string
  path?: string
  color?: string
  description?: string
}

const defaultModules: TopModule[] = [
  {
    id: 'personal-info',
    icon: "👤",
    label: "私人信息",
    path: "/digital-human/info"
  },
  {
    id: 'workspace',
    icon: "💼",
    label: "工作台",
    path: "/workspace"
  },
  {
    id: 'meeting-room',
    icon: "🏢",
    label: "会议室",
    path: "/meeting-room"
  },
  {
    id: 'network-disk',
    icon: "💾",
    label: "网络硬盘",
    path: "/network-disk"
  },
  {
    id: 'health-info',
    icon: "🏥",
    label: "健康信息",
    path: "/health-info"
  },
  {
    id: 'employee-calendar',
    icon: "📅",
    label: "员工日历",
    path: "/employee-calendar"
  },
  {
    id: 'member-info',
    icon: "👥",
    label: "成员信息",
    path: "/member-info"
  },
]

export const useTopModules = (customModules?: TopModule[]) => {
  const navigate = useNavigate()
  const modules = customModules || defaultModules
  
  const handleModuleClick = useCallback((module: TopModule) => {
    if (module.path) {
      navigate(module.path)
    } else {
      console.log(`Module ${module.label} clicked, but no path defined`)
    }
  }, [navigate])
  
  const getModuleById = useCallback((id: string) => {
    return modules.find(module => module.id === id)
  }, [modules])
  
  const getModulesByCategory = useCallback(() => {
    // 可以根据需要扩展分类功能
    return modules
  }, [modules])
  
  return {
    modules,
    handleModuleClick,
    getModuleById,
    getModulesByCategory
  }
}
