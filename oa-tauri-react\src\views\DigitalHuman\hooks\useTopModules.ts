import { useNavigate } from 'react-router'
import { useCallback } from 'react'

export interface TopModule {
  id: string
  icon: string
  label: string
  path?: string
  color?: string
  description?: string
}


// 私人信息
// 工作信息
// 合同信息
// 岗位培训
// 固定资产
// 打卡信息
// 薪资福利
// 假勤信息
// 员工日历
// 离职信息
const defaultModules: TopModule[] = [
  {
    id: 'personal-info',
    icon: "User",
    label: "私人信息",
    path: "/digital-human/info"
  },
  {
    id: 'workspace',
    icon: "LayoutDashboard",
    label: "工作信息",
    path: "/workspace"
  },
  {
    id: 'contract-info',
    icon: "FileSignature",
    label: "合同信息",
    // path: "/contract-info"
  },
  {
    id: 'training',
    icon: "BookOpen",
    label: "岗位培训",
    // path: "/training"
  },
  {
    id: 'fixed-assets',
    icon: "Building",
    label: "固定资产",
    // path: "/fixed-assets"
  },
  {
    id: 'punch-card',
    icon: "Clock",
    label: "打卡信息",
    // path: "/punch-card"
  },
  {
    id: 'salary-welfare',
    icon: "Wallet",
    label: "薪资福利",
    // path: "/salary-welfare"
  },
  {
    id: 'leave-info',
    icon: "Calendar",
    label: "假勤信息",
    // path: "/leave-info"
  },
  {
    id: 'resignation-info',
    icon: "LogOut",
    label: "离职信息",
    // path: "/resignation-info"
  },
]

export const useTopModules = (customModules?: TopModule[]) => {
  const navigate = useNavigate()
  const modules = customModules || defaultModules
  
  const handleModuleClick = useCallback((module: TopModule) => {
    if (module.path) {
      navigate(module.path)
    } else {
      console.log(`Module ${module.label} clicked, but no path defined`)
    }
  }, [navigate])
  
  const getModuleById = useCallback((id: string) => {
    return modules.find(module => module.id === id)
  }, [modules])
  
  const getModulesByCategory = useCallback(() => {
    // 可以根据需要扩展分类功能
    return modules
  }, [modules])
  
  return {
    modules,
    handleModuleClick,
    getModuleById,
    getModulesByCategory
  }
}
