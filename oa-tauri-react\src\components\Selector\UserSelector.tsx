import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';

interface UserItem {
  id: number | string;
  name: string;
}

interface UserSelectorProps {
  visible: boolean;
  value: (number | string)[];
  onChange: (val: (number | string)[]) => void;
  onClose: () => void;
  multiple?: boolean;
  userList: UserItem[];
  loading?: boolean;
  error?: any;
}

export const UserSelector: React.FC<UserSelectorProps> = ({ visible, value, onChange, onClose, multiple = true, userList, loading, error }) => {
  const [selectedKeys, setSelectedKeys] = useState<(number | string)[]>(value || []);
  const [search, setSearch] = useState('');

  useEffect(() => {
    setSelectedKeys(value || []);
  }, [value, visible]);

  const filteredList = (userList || []).filter((user: UserItem) => user.name.includes(search));

  const handleSelect = (id: number | string) => {
    if (multiple) {
      setSelectedKeys(keys =>
        keys.includes(id) ? keys.filter(k => k !== id) : [...keys, id]
      );
    } else {
      setSelectedKeys([id]);
    }
  };

  const handleOk = () => {
    onChange(selectedKeys);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>选择用户</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Input
            placeholder="搜索用户名"
            value={search}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
            className="mb-3"
          />
          <div className="max-h-[300px] overflow-y-auto border rounded-md">
            {loading ? (
              <div className="p-4 text-center text-gray-500">用户列表加载中...</div>
            ) : error ? (
              <div className="p-4 text-center text-red-500">用户列表加载失败：{error.message || '未知错误'}</div>
            ) : filteredList.length === 0 ? (
              <div className="p-4 text-center text-gray-500">无匹配用户</div>
            ) : (
              <ul className="divide-y divide-gray-200">
                {filteredList.map((item: UserItem) => (
                  <li
                    key={item.id}
                    onClick={() => handleSelect(item.id)}
                    className={`flex items-center p-3 cursor-pointer hover:bg-gray-100 ${selectedKeys.includes(item.id) ? 'bg-blue-50' : ''}`}
                  >
                    {multiple && (
                      <Checkbox
                        checked={selectedKeys.includes(item.id)}
                        onCheckedChange={() => handleSelect(item.id)}
                        className="mr-2"
                      />
                    )}
                    {item.name}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={handleCancel}>取消</Button>
          </DialogClose>
          <Button type="submit" onClick={handleOk}>确认</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 