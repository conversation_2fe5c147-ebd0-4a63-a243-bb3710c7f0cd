import React from 'react'
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react'
import { cn } from '@/lib/utils'

// 事项类型定义
export type EventType = 'calendar' | 'attendance' | 'leave' | 'holiday' | 'overtime' | 'task' | 'punishment' | 'meal'

export const eventTypes = [
  { key: 'calendar' as EventType, label: '日历', color: 'bg-blue-500' },
  { key: 'attendance' as EventType, label: '考勤打卡', color: 'bg-green-500' },
  { key: 'leave' as EventType, label: '请假', color: 'bg-red-500' },
  { key: 'holiday' as EventType, label: '假期', color: 'bg-purple-500' },
  { key: 'overtime' as EventType, label: '调休', color: 'bg-orange-500' },
  { key: 'task' as EventType, label: '任务码', color: 'bg-indigo-500' },
  { key: 'punishment' as EventType, label: '处罚', color: 'bg-gray-500' },
  { key: 'meal' as EventType, label: '报餐', color: 'bg-pink-500' },
]

interface CalendarHeaderProps {
  year: number
  monthName: string
  onPreviousMonth: () => void
  onNextMonth: () => void
  onToday: () => void
  activeEventType: EventType
  onEventTypeChange: (type: EventType) => void
  className?: string
}

interface EventTypeButtonProps {
  eventType: { key: EventType; label: string; color: string }
  active: boolean
  onClick: () => void
}

const EventTypeButton: React.FC<EventTypeButtonProps> = ({
  eventType,
  active,
  onClick
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "px-2 py-1.5 sm:px-3 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200",
        "hover:shadow-md transform hover:-translate-y-0.5",
        active
          ? `${eventType.color} text-white shadow-lg`
          : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
      )}
    >
      <span className="truncate">{eventType.label}</span>
    </button>
  )
}

export const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  year,
  monthName,
  onPreviousMonth,
  onNextMonth,
  onToday,
  activeEventType,
  onEventTypeChange,
  className
}) => {
  return (
    <div className={cn("bg-white rounded-lg md:rounded-xl shadow-sm border border-gray-100", className)}>
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between p-4 md:p-6 border-b border-gray-100 gap-4 lg:gap-0">
        {/* 左侧：年月导航 */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
          <div className="flex items-center gap-2 sm:gap-4">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900">{year}年</h2>
            <div className="flex items-center gap-1 sm:gap-2">
              <button
                onClick={onPreviousMonth}
                className="p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 group"
              >
                <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 group-hover:text-gray-900" />
              </button>

              <div className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
                <span className="text-base sm:text-lg font-semibold text-gray-900">{monthName}</span>
              </div>

              <button
                onClick={onNextMonth}
                className="p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 group"
              >
                <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 group-hover:text-gray-900" />
              </button>
            </div>
          </div>

          {/* 今天按钮 */}
          <button
            onClick={onToday}
            className="flex items-center justify-center gap-2 px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 w-fit"
          >
            <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="text-sm sm:text-base font-medium">今天</span>
          </button>
        </div>

        {/* 右侧：事项类型切换按钮 */}
        <div className="flex items-center gap-1 sm:gap-2 justify-center lg:justify-end flex-wrap">
          {eventTypes.map((eventType) => (
            <EventTypeButton
              key={eventType.key}
              eventType={eventType}
              active={activeEventType === eventType.key}
              onClick={() => onEventTypeChange(eventType.key)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
