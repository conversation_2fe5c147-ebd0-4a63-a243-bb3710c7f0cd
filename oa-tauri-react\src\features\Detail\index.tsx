import {  openWindow } from '@/utils'
import { emit } from '@tauri-apps/api/event'

const dataList = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
]

export function Home() {
  const openChild = async (item: any) => {
    // 第一次点击才创建窗口（可选）
    openWindow({
      label: `child-${item.id}`,
      url: `/child/${item.id}`,
      width: 500,
      height: 400,
      initData: item, // 初始化传入第一个点击项
      onCreated: () => {
        emit('update-data', item) // ✅ 通知子窗口立即使用
      },
    })
  }

  const updateChild = (item: any) => {
    emit('update-data', item) // ✅ 再次点击更新子窗口数据
  }

  return (
    <div>
      <h1>主窗口</h1>
      <ul>
        {dataList.map((item) => (
          <li
            key={item.id}
            onClick={() => {
              openChild(item)
              updateChild(item)
            }}
            style={{ cursor: 'pointer', margin: 8 }}
          >
            点击：{item.name}
          </li>
        ))}
      </ul>
    </div>
  )
}

export default Home