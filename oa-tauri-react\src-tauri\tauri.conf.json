{"$schema": "https://schema.tauri.app/config/2", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "frontendDist": "../dist", "devUrl": "http://localhost:1422"}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "productName": "ManagerApp", "mainBinaryName": "ManagerApp", "version": "0.1.0", "identifier": "com.tauri.app", "plugins": {"process": {"active": true}}, "app": {"security": {"csp": null}, "windows": []}}