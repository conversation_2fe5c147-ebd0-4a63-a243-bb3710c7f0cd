import { useEffect, useState } from 'react';
import { getStatistics } from '@/api/roster';

export function useTemplateStatistics(templateId?: number) {
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!templateId) {
      setStatistics(null);
      return;
    }
    setLoading(true);
    setError(null);
    getStatistics({ template_id: templateId })
      .then(res => {
        setStatistics(res.data);
      })
      .catch(err => {
        setError(err?.message || '获取模板详情失败');
      })
      .finally(() => setLoading(false));
  }, [templateId]);

  return { statistics, loading, error };
} 