import { ReactNode, Suspense } from 'react'
import AppErrorPage from '@/features/errors/app-error'
import { ErrorBoundary } from 'react-error-boundary'
import { TooltipProvider } from '@/components/ui/tooltip'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import { ReactPlugin } from '@stagewise-plugins/react'

export default function AppProvider({ children }: { children: ReactNode }) {
    return (
        <Suspense fallback={<>Loading...</>}>
            <ErrorBoundary FallbackComponent={AppErrorPage}>
                <TooltipProvider>{children}
                    {process.env.NODE_ENV === 'development' && (
                        <StagewiseToolbar
                            config={{
                                plugins: [ReactPlugin],
                            }}
                        />
                    )}
                </TooltipProvider>
            </ErrorBoundary>
        </Suspense>
    )
}
