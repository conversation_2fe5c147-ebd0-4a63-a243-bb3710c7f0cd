import React, { useEffect, useState } from 'react';
import { useRoleList } from '../hooks/useRoleList';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface RoleSelectorProps {
  visible: boolean;
  value: number[];
  onChange: (val: number[]) => void;
  onClose: () => void;
  multiple?: boolean;
}

export const RoleSelector: React.FC<RoleSelectorProps> = ({ visible, value, onChange, onClose, multiple = true }) => {
  const { roles, loading, error } = useRoleList();
  const [selected, setSelected] = useState<number[]>(value || []);

  useEffect(() => {
    setSelected(value || []);
  }, [value, visible]);

  const handleSelect = (id: number) => {
    if (multiple) {
      setSelected(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
    } else {
      setSelected([id]);
    }
  };

  const handleConfirm = () => {
    onChange(selected);
    onClose();
  };

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>选择角色</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="max-h-[300px] overflow-y-auto border rounded-md p-2">
            {loading ? (
              <div className="p-4 text-center text-gray-500">加载中...</div>
            ) : error ? (
              <div className="p-4 text-center text-red-500">加载失败</div>
            ) : roles.length === 0 ? (
              <div className="p-4 text-center text-gray-500">暂无角色</div>
            ) : (
              <ul className="divide-y divide-gray-200">
                {roles.map(role => (
                  <li
                    key={role.id}
                    onClick={() => handleSelect(role.id)}
                    className={`flex items-center p-3 cursor-pointer hover:bg-gray-100 ${selected.includes(role.id) ? 'bg-blue-50' : ''}`}
                  >
                    {multiple && (
                      <input
                        type="checkbox"
                        checked={selected.includes(role.id)}
                        onChange={() => handleSelect(role.id)}
                        className="mr-2"
                      />
                    )}
                    {role.name}
                    {role.description ? <span className="text-xs text-gray-400 ml-1">({role.description})</span> : null}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>取消</Button>
          </DialogClose>
          <Button type="submit" onClick={handleConfirm}>确认</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 