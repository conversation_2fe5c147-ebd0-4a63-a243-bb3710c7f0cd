import { useState } from "react"
import { FileSpreadsheet, EyeOff, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface EmployeeData {
  id: number
  sequence: number
  name: string
  birthDate: string
  lunarBirthday: string
  gender: string
  healthStatus: string
  department: string
  jobNumber: string
  hireDate: string
}

interface EmployeeBirthdayListProps {
  data: EmployeeData[]
}

export function EmployeeBirthdayList({ data }: EmployeeBirthdayListProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedItems, setSelectedItems] = useState<number[]>([])

  const filteredData = data.filter(item =>
    item.name.includes(searchQuery) ||
    item.jobNumber.includes(searchQuery) ||
    item.department.includes(searchQuery)
  )

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredData.map(item => item.id))
    } else {
      setSelectedItems([])
    }
  }

  const handleSelectItem = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id])
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id))
    }
  }

  const handleExportExcel = () => {
    // 导出Excel功能
    console.log("导出Excel")
  }

  const handleHide = () => {
    // 隐藏功能
    console.log("隐藏选中项")
  }

  return (
    <div className="p-4">
      {/* 操作按钮 */}
      <div className="mb-2 flex gap-2 justify-between">
        <div className="relative ">
          <Search className="absolute left-3 top-2/5 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="请输入统计名称搜索"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-80 h-8 border text-sm border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="mb-2 flex gap-2">
          <Button onClick={handleExportExcel}
            variant="ghost"
            className="flex items-center px-4 py-2 bg-[#E2E2E3] hover:bg-[#d6d9df] h-8 w-26 text-sm rounded" size="sm">
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            导出Excel
          </Button>
          <Button
            variant="secondary"
            onClick={handleHide}
            className="flex items-center px-4 py-2 bg-[#E2E2E3]  hover:bg-[#d6d9df] text-sm rounded" size="sm"
          >
            <EyeOff className="h-4 w-4 mr-2" />
            隐藏
          </Button>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded shadow overflow-hidden">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedItems.length === filteredData.length && filteredData.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300"
                />
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">序号</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">姓名</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">出生日期</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">生日(农历)</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">性别</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">健康状况</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">部门</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">工号</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">入职日期</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredData.map((employee) => (
              <tr key={employee.id} className="hover:bg-gray-50">
                <td className="px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(employee.id)}
                    onChange={(e) => handleSelectItem(employee.id, e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.sequence}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.name}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.birthDate}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.lunarBirthday}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.gender}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.healthStatus}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.department}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.jobNumber}</td>
                <td className="px-4 py-3 text-sm text-gray-900">{employee.hireDate}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredData.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            暂无数据
          </div>
        )}
      </div>
    </div>
  )
} 