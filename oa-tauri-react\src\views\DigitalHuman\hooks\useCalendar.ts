import { useState, useMemo } from 'react'

export interface CalendarDay {
  date: number
  isCurrentMonth: boolean
  isToday: boolean
  fullDate: Date
  chineseNumber: string
}

export interface CalendarData {
  year: number
  month: number
  monthName: string
  weekDays: string[]
  days: CalendarDay[]
}

// 中文数字转换
const getChineseNumber = (num: number): string => {
  const chinese = [
    '', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
    '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
    '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十', '三十一'
  ]
  return chinese[num] || num.toString()
}

export const useCalendar = (initialDate?: Date) => {
  const [currentDate, setCurrentDate] = useState(initialDate || new Date())
  
  const calendarData = useMemo((): CalendarData => {
    const today = new Date()
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    
    // 获取当月第一天是星期几（0=周日，1=周一...）
    const firstDayOfWeek = firstDay.getDay()
    
    // 生成日历数组
    const days: CalendarDay[] = []
    
    // 添加上个月的日期（填充）
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month, -i)
      days.push({
        date: date.getDate(),
        isCurrentMonth: false,
        isToday: false,
        fullDate: date,
        chineseNumber: getChineseNumber(date.getDate())
      })
    }
    
    // 添加当月的日期
    for (let date = 1; date <= lastDay.getDate(); date++) {
      const currentDateObj = new Date(year, month, date)
      const isToday = date === today.getDate() && 
                     month === today.getMonth() && 
                     year === today.getFullYear()
      
      days.push({
        date,
        isCurrentMonth: true,
        isToday,
        fullDate: currentDateObj,
        chineseNumber: getChineseNumber(date)
      })
    }
    
    // 添加下个月的日期（填充到42个格子）
    const remainingCells = 42 - days.length
    for (let date = 1; date <= remainingCells; date++) {
      const nextMonthDate = new Date(year, month + 1, date)
      days.push({
        date,
        isCurrentMonth: false,
        isToday: false,
        fullDate: nextMonthDate,
        chineseNumber: getChineseNumber(date)
      })
    }
    
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    
    return {
      year,
      month,
      monthName: monthNames[month],
      weekDays,
      days
    }
  }, [currentDate])
  
  const goToPreviousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1))
  }
  
  const goToNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1))
  }
  
  const goToToday = () => {
    setCurrentDate(new Date())
  }
  
  const goToDate = (date: Date) => {
    setCurrentDate(new Date(date.getFullYear(), date.getMonth(), 1))
  }
  
  return {
    calendarData,
    currentDate,
    goToPreviousMonth,
    goToNextMonth,
    goToToday,
    goToDate,
    setCurrentDate
  }
}
