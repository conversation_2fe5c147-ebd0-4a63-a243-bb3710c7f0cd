import React from 'react';
import { useTemplateList, TemplateItem } from '../hooks/useTemplateList';

interface TemplateSidebarProps {
  selectedId?: number;
  onSelect: (id: number) => void;
}

export const TemplateSidebar: React.FC<TemplateSidebarProps> = ({ selectedId, onSelect }) => {
  const { templates, loading, error } = useTemplateList();

  return (
    <div className="w-64 border-l border-gray-200 bg-white h-full overflow-y-auto">
      <div className="p-4 font-bold text-lg border-b">模板导航</div>
      {loading ? (
        <div className="p-4 text-gray-400">加载模板列表中...</div>
      ) : error ? (
        <div className="p-4 text-red-500">{error}</div>
      ) : (
        <ul>
          {templates.map((tpl: TemplateItem) => (
            <li
              key={tpl.id}
              className={`cursor-pointer px-4 py-3 border-b hover:bg-blue-50 transition-colors ${selectedId === tpl.id ? 'bg-blue-100 text-blue-700 font-semibold' : ''}`}
              onClick={() => onSelect(tpl.id)}
            >
              <div className="text-base">{tpl.template_name}</div>
              <div className="text-xs text-gray-500">{tpl.template_description}</div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}; 