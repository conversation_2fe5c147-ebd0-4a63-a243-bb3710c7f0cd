import { TeamListItem, UserItem } from '@/modules/roster/types/team';

export interface TreeNode extends TeamListItem {
  children?: TreeNode[];
}

/**
 * 将扁平结构的团队列表转为树形结构
 * @param list 后端返回的扁平数组
 * @returns 树形结构数组
 */
export function teamListToTree(list: TeamListItem[]): TreeNode[] {
  const teamMap: Record<number, TreeNode> = {};
  const tree: TreeNode[] = [];

  const teamItems = list.filter(item => item.type === 'team');
  const userItems = list.filter(item => item.type === 'user');

  // 第一次遍历 (针对teamItems)：构建团队层级，并初始化map
  teamItems.forEach(item => {
    teamMap[item.id] = { ...item, children: [] };
  });

  // 第二次遍历 (针对teamItems)：构建团队树
  teamItems.forEach(item => {
    const currentNode = teamMap[item.id];
    if (currentNode && item.pid && teamMap[item.pid]) {
      teamMap[item.pid].children!.push(currentNode);
    } else if (currentNode && !item.pid) {
      // 没有pid（pid为0），作为顶层公司
      tree.push(currentNode);
    }
  });

  // 第三次遍历 (针对userItems)：将用户挂载到其父团队下
  userItems.forEach(item => {
    if (item.pid && teamMap[item.pid]) {
      const parentNode = teamMap[item.pid];
      if (parentNode.children) {
        parentNode.children.push({ ...item }); // 用户节点不再有children
      }
    }
  });

  return tree;
}

/**
 * 从团队树中递归提取所有用户节点
 * @param nodes 团队树节点数组
 * @returns 用户节点数组
 */
export function getAllUsersFromTree(nodes: TreeNode[]): UserItem[] {
  const users: UserItem[] = [];

  nodes.forEach(node => {
    if (node.type === 'user') {
      users.push({ id: node.id, name: node.name });
    }
    if (node.children && node.children.length > 0) {
      users.push(...getAllUsersFromTree(node.children));
    }
  });

  return users;
} 