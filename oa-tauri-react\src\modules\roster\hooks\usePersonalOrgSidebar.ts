import { useState, useEffect } from 'react';

export function usePersonalOrgSidebar(teamList: any[]) {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  useEffect(() => {
    // teamList 为根数组
    outer: for (const company of teamList) {
      if (company.children && company.children.length > 0) {
        for (const group of company.children) {
          if (group.children && group.children.length > 0) {
            setSelectedUserId(group.children[0].id);
            break outer;
          }
        }
      }
    }
  }, [teamList]);
  return { selectedUserId, setSelectedUserId };
} 