/****   request.ts   ****/
import axios, { AxiosError, AxiosResponse } from 'axios';
// 创建新的axios实例
const service = axios.create({
  // 公共接口
  // baseURL: "http://[2409:8a44:312:d290:32a4:1df8:ed38:9ef0]:18080/",
  baseURL: localStorage.getItem('baseURL') || "http://192.168.10.88:18080",
  // 超时时间 单位是ms，这里设置了5s的超时时间
  timeout: 5000,
});


// 添加一个请求拦截器
service.interceptors.request.use(
  (config) => {
    // 发请求前做的一些处理，数据转化，配置请求头，设置token,设置loading等

    // const token = localStorage.getItem('token') ?? '';
    const token = localStorage.getItem('token') ?? '2ivgKZGcnK5ISpHbCK7sLAMA7Ar0nm7o';
    // 数据转换，判断数据格式为formdata还是json格式，高版本的axios会默认转换，如果使用的是低版本的需要手动转换
    // json格式
    config.headers['X-App'] = 'admin';
    if (!!token) {
      config.headers['X-Token'] = token;
    }
    // config.data = JSON.stringify(config.data);
    return config;
  },
  (error: AxiosError) => {
    // 出现请求错误，清除toast
    return Promise.reject(error);
  }
);

// 添加一个响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(response);
    const { status, data } = response;
    if (status === 200) {
      // 接口网络请求成功，关闭等待提示
      if (data.code === '0' || data.c === 0) {
        // 接口请求结果正确
        return data;
      } else {
        return Promise.reject(data);
      }
    }
  },
  (error: AxiosError) => {
    console.log(error);
    // if (error.response) {
    //   const { status, data } = error.response;
    //   if (status === 401) {
    //     // 401 清除token信息并跳转到登录页面
    //     // 清除token
    //     localStorage.removeItem('token');
    //     // 跳转到登录页面
    //     // router.replace({
    //     //   path: '/login',
    //   }
    // }

    return Promise.reject(error);
  }
);

export default service;