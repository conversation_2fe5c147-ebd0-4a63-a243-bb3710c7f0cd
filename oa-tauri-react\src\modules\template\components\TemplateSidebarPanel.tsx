import React, { useState } from 'react';
import { TemplateListPanel } from './TemplateListPanel';
import { TemplateDetailPanel } from './TemplateDetailPanel';

export const TemplateSidebarPanel: React.FC = () => {
  const [selectedId, setSelectedId] = useState<number | undefined>(undefined);

  return (
    <div className="flex flex-col h-full w-full bg-white">
      <TemplateListPanel selectedId={selectedId} onSelect={setSelectedId} />
      <div className="flex-1 min-h-0">
        <TemplateDetailPanel templateId={selectedId} />
      </div>
    </div>
  );
}; 