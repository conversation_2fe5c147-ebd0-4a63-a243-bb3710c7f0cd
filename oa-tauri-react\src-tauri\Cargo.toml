[package]
name = "create-tauri-core"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0.3", features = [] }

[dependencies]
tauri = { version = "2.1.1", features = [] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-shell = "2"
tauri-plugin-process = "2.2.0"
structopt = "0.3.26"
log = "0.4.17"
tauri-plugin-log = "2.5.0"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
unstable = ["tauri/unstable"]
