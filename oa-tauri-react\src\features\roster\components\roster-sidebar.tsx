import { useState, useMemo } from "react"
import { cn } from "@/lib/utils"
import { Search, ChevronRight, ChevronDown, Gem, FolderOpen, UserCircle, Monitor } from "lucide-react"

export interface Member {
  id: string
  name: string
  avatar?: string
  position?: string
  role?: string
}
export interface Group {
  id: string
  name: string
  type: "group"
  children: Member[]
}
export interface Company {
  id: string
  name: string
  icon?: string
  children: Group[]
}

export type SidebarData = Company[]

interface RosterSidebarProps {
  className?: string
  onSelect: (payload: { type: 'group' | 'member', id: string }) => void
  selected: { type: 'group' | 'member', id: string } | null
  data: SidebarData
}

export function RosterSidebar({ className, onSelect, selected, data }: RosterSidebarProps) {
  const [search, setSearch] = useState("")
  const [expandedGroups, setExpandedGroups] = useState<{ [key: string]: boolean }>({})
  const [selectedCompanyId, setSelectedCompanyId] = useState(() => data[0]?.id || "")

  // 搜索过滤
  const filteredCompany = useMemo(() => {
    const company = data.find(c => c.id === selectedCompanyId)
    if (!company) return null
    if (!search.trim()) return company
    const lower = search.toLowerCase()
    // 只过滤成员和分组名
    return {
      ...company,
      children: company.children
        .map(group => ({
          ...group,
          children: group.children.filter(member =>
            member.name.toLowerCase().includes(lower) ||
            (member.position && member.position.toLowerCase().includes(lower))
          )
        }))
        .filter(group =>
          group.name.toLowerCase().includes(lower) || group.children.length > 0
        )
    }
  }, [search, selectedCompanyId, data])

  // 展开/收起分组
  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev => ({ ...prev, [groupId]: !prev[groupId] }))
  }

  return (
    <div className={cn("bg-white p-2 border-r h-full flex flex-row", className)}>
      {/* 左侧公司栏 */}
      <div className="flex flex-col border-r pr-2 h-full">
        {/* 搜索框 */}
        <div className="flex items-center bg-gray-100 rounded px-2 py-1 mb-3 mt-1 mx-1">
          <Search className="text-gray-400 mr-2 w-4 h-4" />
          <input
            className="bg-transparent outline-none flex-1 text-sm"
            placeholder="搜索公司"
            value={search}
            onChange={e => setSearch(e.target.value)}
          />
        </div>
        {data.filter(company => company.name.includes(search) || !search.trim()).map(company => (
          <div
            key={company.id}
            className={cn(
              "flex items-center px-2 py-2 rounded cursor-pointer mb-1 mx-1",
              selectedCompanyId === company.id ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"
            )}
            onClick={() => setSelectedCompanyId(company.id)}
          >
            <Gem className="text-blue-400 mr-2 w-5 h-5" />
            <span className="font-medium text-sm flex-1 truncate">{company.name}</span>
          </div>
        ))}
      </div>
      {/* 右侧分组/成员栏 */}
      <div className="w-72 flex-1 flex flex-col pl-4 pr-2 py-2 overflow-auto h-full bg-gray-50">
        {filteredCompany && filteredCompany.children.length === 0 && (
          <div className="text-gray-400 text-center mt-10">暂无分组</div>
        )}
        {filteredCompany && filteredCompany.children.map(group => (
          <div key={group.id} className="mb-4">
            <div
              className={cn(
                "flex items-center px-3 py-2 rounded-lg cursor-pointer shadow-sm",
                (selected && selected.type === 'group' && selected.id === group.id ? "bg-blue-100 text-blue-600" : "bg-white hover:bg-gray-100"),
                expandedGroups[group.id] ? "ring-1 ring-blue-100" : ""
              )}
              onClick={e => {
                e.stopPropagation();
                onSelect({ type: 'group', id: group.id });
                toggleGroup(group.id);
              }}
              style={{ transition: 'background 0.2s' }}
            >
              {expandedGroups[group.id] ? (
                <ChevronDown className="mr-1 text-gray-400 w-4 h-4" />
              ) : (
                <ChevronRight className="mr-1 text-gray-400 w-4 h-4" />
              )}
              <FolderOpen className="text-yellow-500 mr-2 w-5 h-5" />
              <span className="font-medium text-sm flex-1">{group.name}</span>
              <span className="text-xs text-gray-400">[{group.children.length}人]</span>
            </div>
            {/* 成员 */}
            {expandedGroups[group.id] && (
              <ul className="ml-8 mt-2 space-y-1">
                {group.children.map(member => (
                  <li
                    key={member.id}
                    className={cn(
                      "flex items-center px-2 py-1 rounded cursor-pointer text-sm",
                      selected && selected.type === 'member' && selected.id === member.id ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"
                    )}
                    onClick={e => {
                      e.stopPropagation();
                      onSelect({ type: 'member', id: member.id });
                    }}
                  >
                    {member.avatar ? (
                      <img src={member.avatar} alt={member.name} className="w-6 h-6 rounded-full mr-2" />
                    ) : (
                      <UserCircle className="w-6 h-6 text-gray-400 mr-2" />
                    )}
                    <span className="flex-1">{member.name}</span>
                    {member.position && (
                      <span className="ml-2 text-xs text-gray-400">[{member.position}]</span>
                    )}
                    {(member.id === 'user4' || member.id === 'user5') && (
                      <Monitor className="ml-2 w-4 h-4 text-blue-400" />
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
