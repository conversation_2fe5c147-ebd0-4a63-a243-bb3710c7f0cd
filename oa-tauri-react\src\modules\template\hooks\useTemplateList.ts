import { useEffect, useState } from 'react';
import { getTemplateList } from '@/api/roster';

export interface TemplateItem {
  id: number;
  template_name: string;
  template_description: string;
  team_ids: number[];
}

export function useTemplateList() {
  const [templates, setTemplates] = useState<TemplateItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    getTemplateList()
      .then(res => {
        setTemplates(res.data || []);
      })
      .catch(err => {
        setError(err?.message || '获取模板列表失败');
      })
      .finally(() => setLoading(false));
  }, []);

  return { templates, loading, error };
} 