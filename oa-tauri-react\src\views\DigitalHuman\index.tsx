import  { useEffect, useState } from "react"
import { getSelfInfo } from '@/api/roster'
import { DynamicForm } from '@/modules/roster/components/DynamicForm'
import { DynamicTable } from '@/modules/roster/components/DynamicTable'

// 静态配置category列表
const digitalCategories = [
    { category: "UserInfo", description: "基本信息", has_many_records: false },
    { category: "FamilyInfo", description: "家庭信息", has_many_records: true },
    { category: "WorkHistroy", description: "工作经历", has_many_records: true },
    { category: "UserCertificates", description: "获得证书", has_many_records: true },
]

export default function DigitalHumanPage() {
    const [activeCategory, setActiveCategory] = useState(digitalCategories[0].category)
    const [data, setData] = useState<any>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        setLoading(true)
        getSelfInfo({ category: activeCategory })
            .then(res => {
                setData(res.data)
                setError(null)
            })
            .catch(e => {
                setError(e?.message || '加载失败')
                setData(null)
            })
            .finally(() => setLoading(false))
    }, [activeCategory])

    const currentCategory = digitalCategories.find(c => c.category === activeCategory)
    const hasManyRecords = currentCategory?.has_many_records || false

    let content = null
    if (loading) {
        content = <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500">正在加载数字人信息...</div>
    } else if (error) {
        content = <div className="bg-white rounded-lg shadow-sm p-8 text-center text-red-500">加载失败：{error}</div>
    } else if (!data) {
        content = <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500">暂无数据</div>
    } else {
        const fields = data.fields || []
        const records = data.records || []
        const record = records[0] || { values: {} }
        content = hasManyRecords ? (
            <DynamicTable
                fields={fields}
                records={records}
                isEditing={false}
                onChange={() => {}}
                onSaveRow={undefined}
                onDeleteRow={undefined}
            />
        ) : (
            <DynamicForm
                fields={fields}
                values={record.values || {}}
                isEditing={false}
                onChange={() => {}}
            />
        )
    }

    return (
        <div className="bg-gray-50 min-h-screen">
            <div className="bg-white rounded-lg shadow-sm p-8 mb-6">
                <h1 className="text-2xl font-bold mb-2">数字人信息</h1>
                <p className="text-gray-500 mb-4">本页仅供员工本人查看数字人相关信息，无法编辑。</p>
                {/* tab导航 */}
                <div className="flex space-x-2 mb-6">
                    {digitalCategories.map(tab => (
                        <button
                            key={tab.category}
                            className={`px-4 py-2 rounded transition-colors border text-sm font-medium ${activeCategory === tab.category ? 'bg-accent text-accent-foreground border-accent' : 'bg-muted text-muted-foreground border-transparent hover:bg-accent/30'}`}
                            onClick={() => setActiveCategory(tab.category)}
                        >
                            {tab.description}
                        </button>
                    ))}
                </div>
                {content}
            </div>
            <div className="bg-white rounded-lg shadow-sm border-t px-6 py-3 text-xs text-gray-500 flex justify-between">
                <span>最后更新时间: {data?.lastUpdated || '-'}</span>
                <span>仅供本人查看</span>
            </div>
        </div>
    )
}

export const Component = DigitalHumanPage 