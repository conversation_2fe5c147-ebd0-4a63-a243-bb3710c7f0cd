import { useState, useRef } from 'react';
import { getListUserAll } from '@/api/roster';

export interface RoleOption {
  label: string;
  value: number | string;
  raw: any;
}

export function useRoleSelector() {
  const [options, setOptions] = useState<RoleOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadedRef = useRef(false);

  const loadOptions = async () => {
    if (loadedRef.current) return;
    setLoading(true);
    setError(null);
    try {
      const res = await getListUserAll({});
      let records: any[] = [];
      if (res && typeof res === 'object') {
        const data = (res as any).data;
        if (data && Array.isArray(data.records)) {
          records = data.records;
        } else if (Array.isArray((res as any).records)) {
          records = (res as any).records;
        }
      }
      const opts = Array.isArray(records)
        ? records.map((item: any) => ({
            label: Array.isArray(item.name) ? item.name[0] : String(item.name),
            value: item.id,
            raw: item,
          }))
        : [];
      setOptions(opts);
      loadedRef.current = true;
    } catch (e: any) {
      setError(e?.message || '角色列表加载失败');
    } finally {
      setLoading(false);
    }
  };

  const getLabel = (val: any) => {
    if (Array.isArray(val)) {
      return val.map(v => {
        const opt = options.find(o => o.value === v);
        return opt ? opt.label : v;
      }).join(', ');
    } else {
      const opt = options.find(o => o.value === val);
      return opt ? opt.label : val;
    }
  };

  return {
    options,
    loading,
    error,
    loadOptions,
    getLabel,
  };
} 