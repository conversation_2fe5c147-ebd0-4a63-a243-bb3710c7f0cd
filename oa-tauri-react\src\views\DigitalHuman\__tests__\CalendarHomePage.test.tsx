import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import CalendarHomePage from '../CalendarHomePage'

// Mock the hooks
vi.mock('../hooks/useCalendar', () => ({
  useCalendar: () => ({
    calendarData: {
      year: 2024,
      month: 0,
      monthName: '1月',
      weekDays: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      days: [
        {
          date: 1,
          isCurrentMonth: true,
          isToday: true,
          fullDate: new Date(2024, 0, 1),
          chineseNumber: '一'
        }
      ]
    },
    goToPreviousMonth: vi.fn(),
    goToNextMonth: vi.fn(),
    goToToday: vi.fn()
  })
}))

vi.mock('../hooks/useTopModules', () => ({
  useTopModules: () => ({
    modules: [
      {
        id: 'test-module',
        icon: '🤖',
        label: '测试模块',
        path: '/test'
      }
    ],
    handleModuleClick: vi.fn()
  })
}))

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('CalendarHomePage', () => {
  it('renders without crashing', () => {
    renderWithRouter(<CalendarHomePage />)
    expect(screen.getByText('功能模块')).toBeInTheDocument()
  })

  it('displays the current year and month', () => {
    renderWithRouter(<CalendarHomePage />)
    expect(screen.getByText('2024年')).toBeInTheDocument()
    expect(screen.getByText('1月')).toBeInTheDocument()
  })

  it('displays week days', () => {
    renderWithRouter(<CalendarHomePage />)
    expect(screen.getByText('周一')).toBeInTheDocument()
    expect(screen.getByText('周二')).toBeInTheDocument()
    expect(screen.getByText('周日')).toBeInTheDocument()
  })

  it('displays top modules', () => {
    renderWithRouter(<CalendarHomePage />)
    expect(screen.getByText('测试模块')).toBeInTheDocument()
    expect(screen.getByText('🤖')).toBeInTheDocument()
  })

  it('has today button', () => {
    renderWithRouter(<CalendarHomePage />)
    expect(screen.getByText('今天')).toBeInTheDocument()
  })
})
