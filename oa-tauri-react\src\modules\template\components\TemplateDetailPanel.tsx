import React, { useState } from 'react';
import { useTemplateStatistics } from '../hooks/useTemplateStatistics';

interface TemplateDetailPanelProps {
  templateId?: number;
}

// 新增：FamilyInfoTable 子组件
const FamilyInfoTable: React.FC<{
  records: any[];
  fields: any[];
}> = ({ records, fields }) => {
  if (!records || !records.length) {
    return <div className="text-gray-400">暂无家庭信息</div>;
  }
  return (
    <table className="min-w-full border-0 bg-gray-50 text-sm table-fixed">
      <thead>
        <tr>
          <th colSpan={fields.length} className="px-2 py-1 border text-left text-gray-500 bg-gray-50">家庭信息</th>
        </tr>
        <tr>
          {fields.map((f: any) => (
            <th key={f.id} className="px-2 py-1 border text-gray-500 bg-gray-50">{f.field_name}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {records.map((rec: any, idx: number) => (
          <tr key={idx}>
            {fields.map((f: any) => {
              const val = rec[f.id];
              return (
                <td key={f.id} className="px-2 py-1 border">
                  {Array.isArray(val)
                    ? val.map((v: any) => (v !== undefined && v !== null && v !== '' ? String(v) : '-')).join('，')
                    : val !== undefined && val !== null && val !== ''
                    ? String(val)
                    : '-'}
                </td>
              );
            })}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

// 家庭信息卡片组件
const FamilyInfoCards: React.FC<{ records: any[]; fields: any[] }> = ({ records, fields }) => {
  if (!records || !records.length) return <div className="text-gray-400">暂无家庭信息</div>;
  return (
    <div className="flex flex-col gap-2">
      {records.map((rec: any, idx: number) => (
        <div key={idx} className="border rounded p-2 bg-gray-50">
          {fields.map((f: any) => (
            <div key={f.id} className="text-xs text-gray-700 mb-1">
              <span className="font-semibold text-gray-500">{f.field_name}：</span>
              {Array.isArray(rec[f.id])
                ? rec[f.id].map((v: any) => (v !== undefined && v !== null && v !== '' ? String(v) : '-')).join('，')
                : rec[f.id] !== undefined && rec[f.id] !== null && rec[f.id] !== ''
                ? String(rec[f.id])
                : '-'}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

// 家庭信息弹窗组件
const FamilyInfoModal: React.FC<{
  open: boolean;
  onClose: () => void;
  records: any[];
  fields: any[];
}> = ({ open, onClose, records, fields }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-white rounded shadow-lg p-6 min-w-[320px] max-w-[90vw] max-h-[80vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <div className="font-bold">家庭信息</div>
          <button className="text-gray-400 hover:text-gray-700" onClick={onClose}>关闭</button>
        </div>
        <table className="min-w-full border">
          <thead className="bg-gray-50">
            <tr>
              {fields.map((f: any) => (
                <th key={f.id} className="px-2 py-1 border">{f.field_name}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {records.map((rec: any, idx: number) => (
              <tr key={idx}>
                {fields.map((f: any) => {
                  const val = rec[f.id];
                  return (
                    <td key={f.id} className="px-2 py-1 border">
                      {Array.isArray(val)
                        ? val.map((v: any) => (v !== undefined && v !== null && v !== '' ? String(v) : '-')).join('，')
                        : val !== undefined && val !== null && val !== ''
                        ? String(val)
                        : '-'}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export const TemplateDetailPanel: React.FC<TemplateDetailPanelProps> = ({ templateId }) => {
  const { statistics, loading, error } = useTemplateStatistics(templateId);

  const [familyInfoDisplayMode, setFamilyInfoDisplayMode] = useState<'table' | 'card' | 'modal' | 'collapsible-inline'>('modal');
  const [modalOpenIdx, setModalOpenIdx] = useState<number | null>(null);
  const [expandedRowIds, setExpandedRowIds] = useState<Set<number>>(new Set());

  const handleToggleExpand = (userId: number) => {
    setExpandedRowIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const userMap = statistics?.user_map || {};
  const fields = statistics?.fields || [];
  const records = statistics?.records || [];

  // 分类字段
  const userInfoFields = fields.filter((f: any) => f.category === 'UserInfo');
  const familyInfoFields = fields.filter((f: any) => f.category === 'FamilyInfo');

  // 主表数据
  const mainRows = records.map((rec: any) => {
    // UserInfo合并
    let userInfo: Record<string, any> = {};
    if (rec.category_map && rec.category_map.UserInfo) {
      rec.category_map.UserInfo.forEach((obj: any) => {
        Object.entries(obj).forEach(([k, v]) => {
          if (userInfo[k] === undefined) {
            userInfo[k] = v;
          } else {
            if (Array.isArray(userInfo[k])) {
              userInfo[k] = userInfo[k].concat(v);
            } else {
              userInfo[k] = [userInfo[k]].concat(v);
            }
          }
        });
      });
    }
    // FamilyInfo多条
    let familyInfo: any[] = [];
    if (rec.category_map && rec.category_map.FamilyInfo) {
      familyInfo = rec.category_map.FamilyInfo;
    }
    // 用户信息
    const userInfoObj = userMap[rec.user_id];
    return {
      user_id: rec.user_id,
      user_name: userInfoObj ? userInfoObj.name : '-',
      userInfo,
      familyInfo,
    };
  });

  // 渲染UserInfo字段
  const renderUserInfoCell = (val: any, field: any) => {
    if (field.field_type === 'User') {
      if (Array.isArray(val)) {
        return val.map((id: any) => userMap[id]?.name || id).join('，');
      }
      return userMap[val]?.name || val || '-';
    }
    if (Array.isArray(val)) {
      return val.map((v: any) => (v !== undefined && v !== null && v !== '' ? String(v) : '-')).join('，');
    }
    return val !== undefined && val !== null && val !== '' ? String(val) : '-';
  };

  return (
    <div className="flex-1 p-6 bg-gray-50 h-full overflow-y-auto">
      <h2 className="text-xl font-semibold mb-4">模板详情</h2>
      <div className="bg-white rounded shadow p-4 min-h-[120px]">
        <div className="mb-4 flex gap-2 items-center">
          <span className="font-bold">家庭信息展示方式：</span>
          <button className={`px-2 py-1 rounded ${familyInfoDisplayMode === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`} onClick={() => setFamilyInfoDisplayMode('table')}>嵌套表格</button>
          <button className={`px-2 py-1 rounded ${familyInfoDisplayMode === 'card' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`} onClick={() => setFamilyInfoDisplayMode('card')}>卡片式</button>
          <button className={`px-2 py-1 rounded ${familyInfoDisplayMode === 'modal' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`} onClick={() => setFamilyInfoDisplayMode('modal')}>弹窗/折叠</button>
          <button className={`px-2 py-1 rounded ${familyInfoDisplayMode === 'collapsible-inline' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`} onClick={() => setFamilyInfoDisplayMode('collapsible-inline')}>可折叠</button>
        </div>
        {loading ? (
          <div>加载模板详情中...</div>
        ) : error ? (
          <div className="text-red-500">{error}</div>
        ) : !fields.length ? (
          <div className="text-gray-400">暂无字段信息</div>
        ) : (
          <>
            {/* <div className="mb-2 font-bold">字段信息</div>
            <table className="min-w-full mb-4 border">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1 border">字段名</th>
                  <th className="px-2 py-1 border">类型</th>
                  <th className="px-2 py-1 border">必填</th>
                  <th className="px-2 py-1 border">系统字段</th>
                  <th className="px-2 py-1 border">备注</th>
                </tr>
              </thead>
              <tbody>
                {fields.map((f: any) => (
                  <tr key={f.id}>
                    <td className="px-2 py-1 border">{f.field_name}</td>
                    <td className="px-2 py-1 border">{f.field_type}</td>
                    <td className="px-2 py-1 border">{f.is_required ? '是' : '否'}</td>
                    <td className="px-2 py-1 border">{f.is_system ? '是' : '否'}</td>
                    <td className="px-2 py-1 border">{f.category || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table> */}
            <div className="mb-2 font-bold">记录信息</div>
            {mainRows.length ? (
              <table className="min-w-full border">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-2 py-1 border">用户ID</th>
                    <th className="px-2 py-1 border">姓名</th>
                    {userInfoFields.map((f: any) => (
                      <th key={f.id} className="px-2 py-1 border">{f.field_name}</th>
                    ))}
                    <th className="px-2 py-1 border">家庭信息</th>
                  </tr>
                </thead>
                <tbody>
                  {mainRows.map((row: any, idx: number) => (
                    <React.Fragment key={idx}>
                      <tr>
                        <td className="px-2 py-1 border">{row.user_id}</td>
                        <td className="px-2 py-1 border">{row.user_name}</td>
                        {userInfoFields.map((f: any) => (
                          <td key={f.id} className="px-2 py-1 border">
                            {renderUserInfoCell(row.userInfo[f.id], f)}
                          </td>
                        ))}
                        <td className="px-2 py-1 border min-w-[200px]">
                          {/* 嵌套表格模式 */}
                          {familyInfoDisplayMode === 'table' && (
                            <FamilyInfoTable records={row.familyInfo} fields={familyInfoFields} />
                          )}
                          {/* 卡片式模式 */}
                          {familyInfoDisplayMode === 'card' && (
                            <FamilyInfoCards records={row.familyInfo} fields={familyInfoFields} />
                          )}
                          {/* 弹窗模式 */}
                          {familyInfoDisplayMode === 'modal' && (
                            row.familyInfo && row.familyInfo.length > 0 ? (
                              <>
                                <button className="px-2 py-1 bg-blue-100 rounded text-blue-700 hover:bg-blue-200" onClick={() => setModalOpenIdx(idx)}>查看</button>
                                <FamilyInfoModal open={modalOpenIdx === idx} onClose={() => setModalOpenIdx(null)} records={row.familyInfo} fields={familyInfoFields} />
                              </>
                            ) : (
                              <span className="text-gray-400">暂无家庭信息</span>
                            )
                          )}
                          {/* 可折叠行内模式 */}
                          {familyInfoDisplayMode === 'collapsible-inline' && (
                            row.familyInfo && row.familyInfo.length > 0 ? (
                              <button 
                                className="px-2 py-1 bg-blue-100 rounded text-blue-700 hover:bg-blue-200 flex items-center gap-1"
                                onClick={() => handleToggleExpand(row.user_id)}
                              >
                                {expandedRowIds.has(row.user_id) ? '收起' : '展开'}
                                <span className={`transform transition-transform duration-200 ${expandedRowIds.has(row.user_id) ? 'rotate-180' : 'rotate-0'}`}>▼</span>
                              </button>
                            ) : (
                              <span className="text-gray-400">暂无家庭信息</span>
                            )
                          )}
                        </td>
                      </tr>
                      {/* 可折叠模式下的详细信息行 */}
                      {familyInfoDisplayMode === 'collapsible-inline' && expandedRowIds.has(row.user_id) && row.familyInfo && row.familyInfo.length > 0 && (
                        <tr>
                          <td colSpan={3 + userInfoFields.length} className="p-0 border-t bg-gray-50">
                            <div className="p-4">
                              <FamilyInfoTable records={row.familyInfo} fields={familyInfoFields} />
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-gray-400">暂无记录信息</div>
            )}
          </>
        )}
      </div>
    </div>
  );
}; 