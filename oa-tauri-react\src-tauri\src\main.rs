// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]
pub mod cli;
use crate::cli::Cli;
use log::*;
use structopt::*;
use tauri::{WebviewUrl, WebviewWindowBuilder};

// Learn more about Tauri commands at https://tauri.app/v1/guides/features/command
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

fn main() {
    let cli = Cli::from_args();
    let page = cli.page;
    let token = cli.token;
    let uri = cli.uri;

    let log_plugin = tauri_plugin_log::Builder::new()
        .target(tauri_plugin_log::Target::new(
            tauri_plugin_log::TargetKind::Folder {
                path: std::path::PathBuf::from("logs"),
                file_name: None,
            },
        ))
        .build();

    tauri::Builder::default()
        .setup(move |app| {
            let win_builder = WebviewWindowBuilder::new(
                app,
                "main",
                WebviewUrl::App(format!("{page}?token={token}&uri={uri}").as_str().into()),
            )
            .title("Transparent Titlebar Window")
            .inner_size(800.0, 600.0);
            let result = win_builder.build();
            if let Err(e) = result {
                error!("Error while creating window: {}", e);
            }
            Ok(())
        })
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_process::init())
        .plugin(log_plugin)
        .invoke_handler(tauri::generate_handler![greet])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
