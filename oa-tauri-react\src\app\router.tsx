import { createBrowserRouter, RouterProvider } from 'react-router'

const createAppRouter = () =>
    createBrowserRouter([
        {
            path: '/',
            lazy: () => import('@/app/routes/home')
        },
        // {
        //     path: '/child/:id',
        //     lazy: () => import('@/views/Home/index')
        // },
        {
            path: '/roster/:departmentId?/:view?/:id?',
            lazy: () => import('@/views/Roster/RosterPage')
        },
        {
            path: '/admnstrtn',
            lazy: () => import('@/views/Administration/index')
        },
        {
            path: '/employee/info',
            lazy: () => import('@/views/Home/EmployeeInfoPage')
        },
        {
            path: '/digital-human',
            lazy: () => import('@/views/DigitalHuman/CalendarHomePage').then(m => ({ Component: m.Component })),
        },
        {
            path: '/digital-human/info',
            lazy: () => import('@/views/DigitalHuman').then(m => ({ Component: m.Component })),
        },
        {
            path: '*',
            lazy: () => import('@/app/routes/not-found')
        }
    ])

export default function AppRouter() {
    return <RouterProvider router={createAppRouter()} />
}
