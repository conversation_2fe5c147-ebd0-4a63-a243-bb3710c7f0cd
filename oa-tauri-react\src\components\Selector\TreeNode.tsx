import React, { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TeamNode {
  id: number | string;
  name: string;
  children?: TeamNode[];
}

interface TreeNodeProps {
  node: TeamNode;
  multiple: boolean;
  selectedKeys: (number | string)[];
  onSelect: (id: number | string) => void;
}

const TreeNode: React.FC<TreeNodeProps> = ({ node, multiple, selectedKeys, onSelect }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const isSelected = selectedKeys.includes(node.id);

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleNodeClick = () => {
    onSelect(node.id);
  };

  return (
    <div>
      <div
        className={cn(
          "flex items-center cursor-pointer p-2 hover:bg-gray-100 rounded-md",
          isSelected && "bg-blue-50"
        )}
      >
        {node.children && node.children.length > 0 && (
          <ChevronRight
            className={cn("h-4 w-4 mr-1 transition-transform", isExpanded && "rotate-90")}
            onClick={handleToggleExpand}
          />
        )}
        {multiple ? (
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelect(node.id)}
            className="mr-2"
          />
        ) : null}
        <span onClick={handleNodeClick}>{node.name}</span>
      </div>
      {isExpanded && node.children && node.children.length > 0 && (
        <div className="ml-4 border-l pl-2">
          {node.children.map(child => (
            <TreeNode
              key={child.id}
              node={child}
              multiple={multiple}
              selectedKeys={selectedKeys}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TreeNode; 