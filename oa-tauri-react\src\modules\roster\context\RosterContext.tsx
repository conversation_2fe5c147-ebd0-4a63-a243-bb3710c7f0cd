import React, { createContext, useContext, useMemo } from 'react';
import { useRosterClassifyList } from '../hooks/useClassifyList';
import { useRosterTeamList } from '../hooks/useTeamList';
import { ClassifyItem } from '../types/classify';
import { TeamListItem } from '../types/team';

interface TreeNode extends TeamListItem {
  children?: TreeNode[];
}

interface RosterContextValue {
  classifyList: ClassifyItem[];
  teamList: TreeNode[];
  loading: boolean;
  error: Error | null;
  refresh: () => void;
}

const RosterContext = createContext<RosterContextValue | undefined>(undefined);

export const RosterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    data: classifyList,
    loading: classifyLoading,
    error: classifyError,
    refresh: refreshClassify
  } = useRosterClassifyList();
  const {
    data: teamList,
    loading: teamLoading,
    error: teamError,
    refresh: refreshTeam
  } = useRosterTeamList();

  const loading = classifyLoading || teamLoading;
  const error = classifyError || teamError;
  const refresh = () => {
    refreshClassify();
    refreshTeam();
  };

  const value = useMemo(() => ({
    classifyList,
    teamList,
    loading,
    error,
    refresh
  }), [classifyList, teamList, loading, error]);

  return (
    <RosterContext.Provider value={value}>
      {children}
    </RosterContext.Provider>
  );
};

export function useRosterContext() {
  const ctx = useContext(RosterContext);
  if (!ctx) throw new Error('useRosterContext must be used within a RosterProvider');
  return ctx;
} 