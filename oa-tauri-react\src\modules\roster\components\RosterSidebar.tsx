import React, { useState, useMemo, useCallback } from "react";
import { cn } from "@/lib/utils";
import { Search, ChevronRight, ChevronDown, Gem, UserCircle } from "lucide-react";
import { MdFolder } from 'react-icons/md';
import { useRosterContext } from '../context/RosterContext';

interface PersonalOrgSidebarProps {
  className?: string;
  onSelectUser: (userId: number) => void;
  selectedUserId: number | null;
}

export const PersonalOrgSidebar: React.FC<PersonalOrgSidebarProps> = ({ className, onSelectUser, selectedUserId }) => {
  const { teamList } = useRosterContext();
  const [search, setSearch] = useState("");

  // 过滤出顶层总公司 (type === 'team' 且 pid === 0)
  const topLevelCompanies = useMemo(() => {
    return teamList.filter((item: any) => item.type === 'team' && item.pid === 0);
  }, [teamList]);

  const [selectedCompanyId, setSelectedCompanyId] = useState(() => topLevelCompanies[0]?.id || "");

  // 展开状态管理：key为节点id，值为是否展开
  const [expanded, setExpanded] = useState<{ [key: string]: boolean }>({});
  const toggleExpand = useCallback((id: string | number) => {
    setExpanded(prev => ({ ...prev, [id]: !prev[id] }));
  }, []);

  // 搜索过滤：递归过滤所有分组/成员/分组名/职位
  const filterTree = (node: any, lower: string): any => {
    if (!node) return null;
    if (node.type === 'user') {
      if (
        node.name.toLowerCase().includes(lower) ||
        (node.position && node.position.toLowerCase().includes(lower))
      ) {
        return node;
      }
      return null;
    }
    // 分组/公司
    const filteredChildren = (node.children || [])
      .map((child: any) => filterTree(child, lower))
      .filter(Boolean);
    if (
      node.name.toLowerCase().includes(lower) ||
      filteredChildren.length > 0
    ) {
      return { ...node, children: filteredChildren };
    }
    return null;
  };

  const filteredCompany = useMemo(() => {
    const company = topLevelCompanies.find((c: any) => c.id === selectedCompanyId);
    if (!company) return null;
    if (!search.trim()) return company;
    const lower = search.toLowerCase();
    return filterTree(company, lower);
  }, [search, selectedCompanyId, topLevelCompanies]);

  // 递归渲染树节点
  const renderTreeNode = (node: any, depth = 0) => {
    if (!node) return null;
    if (node.type === 'user') {
      return (
        <li
          key={node.id}
          className={cn(
            "flex items-center px-2 py-1 rounded cursor-pointer text-sm",
            selectedUserId === node.id ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"
          )}
          onClick={e => {
            e.stopPropagation();
            onSelectUser(node.id);
          }}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
        >
          {node.avatar && false ? (
            <img src={node.avatar} alt={node.name} className="w-6 h-6 rounded-full mr-2" />
          ) : (
            <UserCircle className="w-6 h-6 text-gray-400 mr-2 scale-80" />
          )}
          <span className="flex-1">{node.name}</span>
          {node.position && (
            <span className="ml-2 text-xs text-gray-400">[{node.position}]</span>
          )}
        </li>
      );
    }
    // 分组/公司
    const isExpanded = expanded[node.id] || !!search;
    return (
      <div key={node.id} className="mb-1">
        <div
          className={cn(
            "flex items-center px-2 py-1 rounded cursor-pointer",
            isExpanded ? "bg-gray-100" : "hover:bg-gray-50"
          )}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => toggleExpand(node.id)}
        >
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-gray-400 mr-1" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-400 mr-1" />
          )}
          <MdFolder className="mr-2 w-5 h-5 text-[#7da4d1]" />
          <span className="font-medium text-sm flex-1 truncate">{node.name}</span>
          {node.children && node.children.length > 0 && node.children.filter((c: any) => c.type === 'user').length > 0 && (
            <span className="text-xs text-gray-400">[{node.children.filter((c: any) => c.type === 'user').length}人]</span>
          )}
        </div>
        {isExpanded && node.children && node.children.length > 0 && (
          <ul className="mt-1">
            {node.children.map((child: any) => renderTreeNode(child, depth + 1))}
          </ul>
        )}
      </div>
    );
  };

  return (
    <div className={cn("bg-white  border-r h-full flex flex-row", className)}>
      {/* 左侧公司栏 */}
      <div className="flex flex-col border-r border-t pr-2 pt-2 h-full">
        {/* 搜索框 */}
        <div className="flex items-center bg-gray-100 rounded px-2 py-1 mb-3 mt-1 mx-1">
          <Search className="text-gray-400 mr-2 w-4 h-4" />
          <input
            className="bg-transparent outline-none flex-1 text-sm"
            placeholder="搜索公司"
            value={search}
            onChange={e => setSearch(e.target.value)}
          />
        </div>
        {topLevelCompanies.filter((company: any) => company.name.includes(search) || !search.trim()).map((company: any) => (
          <div
            key={company.id}
            className={cn(
              "flex items-center px-2 py-2 rounded cursor-pointer mb-1 w-full",
              selectedCompanyId === company.id ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"
            )}
            onClick={() => setSelectedCompanyId(company.id)}
          >
            <Gem className="text-blue-400 mr-2 w-5 h-5" />
            <span className="font-medium text-sm flex-1 truncate">{company.name}</span>
            <ChevronRight className="w-4 h-4 text-gray-400 mr-1" />
          </div>
        ))}
      </div>
      {/* 右侧分组/成员栏 */}
      <div className="w-72 flex-1 flex flex-col pl-4 pr-2 py-2 overflow-auto border-t h-full bg-gray-50">
        {!filteredCompany || !filteredCompany.children || filteredCompany.children.length === 0 ? (
          <div className="text-gray-400 text-center mt-10">暂无分组</div>
        ) : (
          filteredCompany.children.map((node: any) => renderTreeNode(node, 0))
        )}
      </div>
    </div>
  );
}; 